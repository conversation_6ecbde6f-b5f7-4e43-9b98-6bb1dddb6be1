package utils

import (
	"account/global"
	"encoding/json"
	"time"

	mq_http_sdk "github.com/aliyunmq/mq-http-go-sdk"
)

func NewMQProducer() mq_http_sdk.MQProducer {
	client := mq_http_sdk.NewAliyunMQClientWithTimeout(
		global.CONFIG.MQ.EndPoint,
		global.CONFIG.MQ.AccessKeyId,
		global.CONFIG.MQ.AccessKeySecret,
		"",
		time.Second*time.Duration(5))

	producer := client.GetProducer(global.CONFIG.MQ.InstanceId, global.CONFIG.MQ.Topic)
	return producer
}

func MQPublish(producer mq_http_sdk.MQProducer, data interface{}, msgtag string) (msgId string, err error) {
	body, _ := json.Marshal(data)
	msgreq := mq_http_sdk.PublishMessageRequest{
		MessageBody: string(body),
		MessageTag:  msgtag,
	}
	resp, err := producer.PublishMessage(msgreq)
	return resp.MessageId, err
}
