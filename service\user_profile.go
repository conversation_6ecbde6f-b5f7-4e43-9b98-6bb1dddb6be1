package service

import (
	"account/global"
	"account/model"
	"context"
	"strings"
)

func GetProfileByUid(uid uint) (*model.UserProfile, error) {
	var p model.UserProfile
	err := global.DB.Model(&model.UserProfile{}).First(&p, "uid=?", uid).Error
	return &p, err
}

func CreateProfile(p *model.UserProfile) error {
	return global.DB.Model(&model.UserProfile{}).Create(p).Error
}

func SaveProfile(p *model.UserProfile) error {
	return global.DB.Model(&model.UserProfile{}).Where("uid=?", p.Uid).Limit(1).Save(p).Error
}

func GetProfileByUids(uids string) (list []model.UserProfile, err error) {
	ids := strings.Split(uids, ",")
	err = global.DB.Model(&model.UserProfile{}).Find(&list, "uid in (?)", ids).Error
	return
}

func DeleteProfileByUid(uid uint) error {
	// return global.DB.Model(&model.UserProfile{}).Delete(&model.UserProfile{}, "uid=?", uid).Error
	return global.DB.Exec("DELETE FROM `user_profile` WHERE `uid`=? Limit 1", uid).Error
}

func SearchByUrname(username string) ([]model.UserProfile, error) {
	username = username + "%"
	var list []model.UserProfile
	err := global.DB.Model(&model.UserProfile{}).Find(&list, "username like ? or realname like ?", username, username).Error
	return list, err
}

func SearchByRealname(realname string) ([]model.UserProfile, error) {
	realname = realname + "%"
	var list []model.UserProfile
	err := global.DB.Model(&model.UserProfile{}).Find(&list, "realname like ?", realname).Error
	return list, err
}

func GetProfileMapByUids(uids []uint, field ...string) (m map[uint]model.UserProfile, err error) {
	if len(field) == 0 {
		field = []string{"uid", "realname"}
	}
	var list []model.UserProfile
	err = global.DB.Model(&model.UserProfile{}).Select(strings.Join(field, ",")).Find(&list, "uid in (?)", uids).Error
	if err != nil {
		return
	}
	m = make(map[uint]model.UserProfile)
	for i := range list {
		m[uint(list[i].Uid)] = list[i]
	}
	return
}

func CreateProfileCtx(c context.Context, p *model.UserProfile) error {
	return global.GetDB(c).Model(&model.UserProfile{}).Create(p).Error
}

func UpdateProfile(c context.Context, p *model.UserProfile) error {
	return global.GetDB(c).Model(p).Where("uid = ?", p.Uid).Updates(p).Error
}
