package response

// CtdidAuthResponse 网络身份认证响应结构
type CtdidAuthResponse struct {
	Sign       string                   `json:"sign"`       // 签名
	BizPackage CtdidAuthResponsePackage `json:"bizPackage"` // 响应结果业务数据
}

// CtdidAuthResponsePackage 网络身份认证响应业务数据
type CtdidAuthResponsePackage struct {
	BizSerialNo string                `json:"bizSerialNo"` // 业务流水号
	ResultCode  string                `json:"resultCode"`  // 响应结果码
	ResultDesc  string                `json:"resultDesc"`  // 响应结果描述
	BizSeq      string                `json:"bizSeq"`      // 业务序列号
	Data        *CtdidAuthResponseData `json:"data,omitempty"` // 响应数据，只有成功时返回
}

// CtdidAuthResponseData 认证成功时的响应数据
type CtdidAuthResponseData struct {
	PID                string `json:"PID"`                          // 网络身份认证凭据数据
	PhotoCompareScore  string `json:"photoCompareScore,omitempty"`  // 人脸相似度分值
	EncryptedIdInfo    string `json:"encryptedIdInfo,omitempty"`    // 加密年龄标识
}

// 响应结果码常量
const (
	CTDID_SUCCESS                    = "C0000000" // 成功
	CTDID_PARAM_ERROR               = "C0201999" // 参数异常
	CTDID_SYSTEM_ERROR              = "S0200001" // 系统异常
	CTDID_TIMEOUT                   = "C0299002" // 业务超时
	CTDID_MODE_ERROR                = "C0201004" // 模式异常
	CTDID_ORG_ID_ERROR              = "C0201009" // 机构ID异常
	CTDID_APP_ID_ERROR              = "C0201010" // 应用ID异常
	CTDID_CREDENTIAL_NOT_FOUND      = "C0208001" // 网络身份认证凭证未查到
	CTDID_INVALID_CREDENTIAL        = "C0208002" // 无效网络身份认证凭证
	CTDID_CREDENTIAL_EXPIRED        = "C0208003" // 网络身份认证凭证已过期
	CTDID_FACE_COMPARE_FAILED       = "C0207001" // 人脸比对未通过
	CTDID_FACE_IMAGE_INVALID        = "C0207002" // 人脸图像不合格
	CTDID_PASSWORD_ERROR            = "C0210001" // 网络身份认证凭证口令错误
	CTDID_BIZ_SEQ_ERROR             = "C0201003" // 业务序列号异常
	CTDID_VERIFY_FAILED             = "C0204001" // 验签失败
	CTDID_ACCESS_DENIED             = "C0203001" // 接入权限核验失败
	CTDID_CONCURRENT_LIMIT          = "C0203002" // 服务并发达到上限
	CTDID_DAILY_LIMIT_EXCEEDED      = "C0203003" // 服务当日调用次数已耗尽
	CTDID_IDENTITY_NOT_FOUND        = "C0206004" // 身份信息未查到
	CTDID_CERT_ERROR                = "C0201032" // 加密证书异常
)

// GetResultMessage 获取结果码对应的描述信息
func GetResultMessage(code string) string {
	messages := map[string]string{
		CTDID_SUCCESS:                   "成功",
		CTDID_PARAM_ERROR:              "参数异常",
		CTDID_SYSTEM_ERROR:             "系统异常",
		CTDID_TIMEOUT:                  "业务超时",
		CTDID_MODE_ERROR:               "模式异常",
		CTDID_ORG_ID_ERROR:             "机构ID异常",
		CTDID_APP_ID_ERROR:             "应用ID异常",
		CTDID_CREDENTIAL_NOT_FOUND:     "网络身份认证凭证未查到",
		CTDID_INVALID_CREDENTIAL:       "无效网络身份认证凭证",
		CTDID_CREDENTIAL_EXPIRED:       "网络身份认证凭证已过期",
		CTDID_FACE_COMPARE_FAILED:      "人脸比对未通过",
		CTDID_FACE_IMAGE_INVALID:       "人脸图像不合格",
		CTDID_PASSWORD_ERROR:           "网络身份认证凭证口令错误",
		CTDID_BIZ_SEQ_ERROR:            "业务序列号异常",
		CTDID_VERIFY_FAILED:            "验签失败",
		CTDID_ACCESS_DENIED:            "接入权限核验失败",
		CTDID_CONCURRENT_LIMIT:         "服务并发达到上限",
		CTDID_DAILY_LIMIT_EXCEEDED:     "服务当日调用次数已耗尽",
		CTDID_IDENTITY_NOT_FOUND:       "身份信息未查到",
		CTDID_CERT_ERROR:               "加密证书异常",
	}
	if msg, exists := messages[code]; exists {
		return msg
	}
	return "未知错误"
}

// 年龄标识映射
var AgeGroupMapping = map[string]string{
	"03": "年满8周岁未满12周岁",
	"04": "年满12周岁未满14周岁",
	"05": "年满14周岁未满16周岁",
	"06": "年满16周岁未满18周岁",
	"07": "年满18周岁未满20周岁",
	"08": "年满20周岁未满22周岁",
	"09": "年满22周岁未满45周岁",
	"10": "年满45周岁未满55周岁",
	"11": "年满55周岁未满60周岁",
	"12": "年满60周岁未满70周岁",
	"13": "年满70周岁未满75周岁",
	"14": "年满75周岁以上",
}
