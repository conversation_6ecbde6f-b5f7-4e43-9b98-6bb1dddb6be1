package service

import (
	"account/global"
	"account/model"
)

func ExistBadword<PERSON>y<PERSON><PERSON>(key string) bool {
	var bw model.Badword
	global.DB.Model(&bw).Select("id").First(&bw, "`key`=?", key)
	return bw.ID > 0
}

func ExistBadwordLike(key string) bool {
	var bw model.Badword
	global.DB.Model(&bw).Select("id").First(&bw, "`key` like ?", key+"%")
	return bw.ID > 0
}

func AddBadword(words []string) (err error) {
	list := make([]model.Badword, len(words))
	for i, w := range words {
		list[i].Key = w
	}
	return global.DB.Model(&model.Badword{}).CreateInBatches(&list, len(list)).Error
}

func DelBadword(words []string) (err error) {
	return global.DB.Exec("Delete From badwords Where `key` in (?) Limit ?", words, len(words)).Error
}
