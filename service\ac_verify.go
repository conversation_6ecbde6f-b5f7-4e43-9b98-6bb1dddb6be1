package service

import (
	"fmt"
	"time"

	"account/global"
	"account/model"

	"go.uber.org/zap"
)

func CreateVerify(v *model.AcVerify) error {
	return global.DB.Model(&model.AcVerify{}).Create(v).Error
}

func GetVerifyById(id string) (verify *model.AcVerify, err error) {
	var v model.AcVerify
	err = global.DB.Model(&model.AcVerify{}).First(&v, "id=?", id).Error
	return &v, err
}

func SetVerifyStatus(id uint, status int) (err error) {
	// return global.DB.Model(&model.AcVerify{}).Where("id=?", id).Update("status", status).Error
	return global.DB.Exec("Update `ac_verify_queue` Set `status`=? Where `id`=? Limit 1", status, id).Error
}

// DropVerify 删除过期验证码
func DropVerify() error {
	timestamp := time.Now().Unix()
	// return global.DB.Model(&model.AcVerify{}).Where("expire<?", timestamp).Delete(&model.AcVerify{}).Error
	return global.DB.Exec("DELETE FROM `ac_verify_queue` WHERE `expire`<?", timestamp).Error
}

func CheckVerifyLimit(username string, now int64, t int, limit ...int) bool {
	min := 60
	if len(limit) > 0 {
		min = limit[0]
	}
	var v model.AcVerify
	global.DB.Model(&model.AcVerify{}).Order("expire DESC").Limit(1).
		Find(&v, "username=? and status=1 and type=?", username, t)

	expire := global.MOBILE_VAILD
	if t == 0 || t == 1 {
		expire = global.EMAIL_VAILD
	}
	sendTime := v.Expire - int64(expire)
	if now-sendTime < int64(min) && now-sendTime > -int64(min) {
		return false
	}
	return true
}

func CheckMobileVerifyLimit(mobile string, appid string, vType int) (ok bool) {
	if global.Redis != nil {
		key := fmt.Sprintf("account:%s:%s:%d", appid, mobile, vType)
		ok, err := global.Redis.SetNX(key, mobile, time.Minute).Result()
		if err == nil {
			return ok
		}
		global.LOG.Info("CheckMobileVerifyLimit", zap.Error(err))
	}
	ok = CheckVerifyLimit(mobile, time.Now().Unix(), vType)
	return
}
