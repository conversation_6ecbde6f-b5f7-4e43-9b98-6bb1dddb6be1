package middleware

import (
	"account/global"
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func BodyLog() gin.HandlerFunc {
	return func(c *gin.Context) {
		field := make([]zap.Field, 0, 10)
		field = append(field, zap.String("uri", c.Request.URL.RequestURI()), zap.String("content-type", c.ContentType()))
		if c.Request.Method != http.MethodGet {
			c.Request.FormValue("")
			switch c.ContentType() {
			case gin.MIMEMultipartPOSTForm, gin.MIMEPOSTForm:
				for k := range c.Request.Form {
					field = append(field, zap.Strings(k, c.Request.Form[k]))
				}
			case gin.MIMEJSON:
				body, err := ioutil.ReadAll(c.Request.Body)
				if err != nil {
					global.LOG.Error("read body from request error:", zap.Error(err))
				} else {
					c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))
					data := make(map[string]interface{})
					json.Unmarshal(body, &data)
					for k := range data {
						field = append(field, zap.Any(k, data[k]))
					}
				}
			}
			global.LOG.Info("body_log", field...)
		}
		c.Next()
	}
}
