# Content-Type 问题排查指南

## 问题描述

国标要求接口使用 `Content-Type: application/json`，但可能出现验证失败的情况。

## 可能的原因

### 1. 客户端发送的 Content-Type 不正确

**常见情况**:
- `application/json; charset=utf-8` (带字符集)
- `application/json;charset=utf-8` (无空格)
- `Application/Json` (大小写不匹配)
- `text/json` (错误的媒体类型)

### 2. 代理或中间件修改了请求头

某些代理服务器或中间件可能会修改请求头。

### 3. 客户端库的默认行为

某些 HTTP 客户端库会自动添加字符集信息。

## 当前的解决方案

代码已经修改为更宽松的验证：

```go
// 使用前缀匹配，支持带 charset 的情况
if !strings.HasPrefix(contentType, "application/json") {
    // 验证失败
}
```

这样可以接受：
- `application/json`
- `application/json; charset=utf-8`
- `application/json;charset=utf-8`

## 调试信息

### 服务端日志

启动应用后，查看日志中的以下信息：

```
收到请求的Content-Type: application/json
发送请求头: Content-Type=application/json
```

### 客户端测试

使用 curl 测试接口：

```bash
# 正确的请求
curl -X POST http://localhost:8000/api/v1/ctdid/auth \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# 带字符集的请求（也应该被接受）
curl -X POST http://localhost:8000/api/v1/ctdid/auth \
  -H "Content-Type: application/json; charset=utf-8" \
  -d '{"test": "data"}'
```

## 进一步排查

### 1. 检查实际的请求头

在控制器中添加了详细的日志，可以看到实际收到的 Content-Type：

```go
global.LOG.Info("收到请求的Content-Type", zap.String("content-type", contentType))
```

### 2. 检查客户端代码

确认客户端发送请求时正确设置了 Content-Type：

```go
req.Header.Set("Content-Type", "application/json")
```

### 3. 网络抓包

使用 Wireshark 或 tcpdump 抓包查看实际的 HTTP 请求头。

## 临时解决方案

如果仍有问题，可以临时注释掉 Content-Type 验证：

```go
// 临时注释掉验证
// if !strings.HasPrefix(contentType, "application/json") {
//     response.FailWithErrcode(response.E_PARAM, "Content-Type必须为application/json，当前为: "+contentType, c)
//     return
// }
```

## 国标要求对比

根据国标文档：
- 协议：HTTP
- 数据格式：JSON
- 请求方式：POST
- Content-Type：application/json
- 编码：UTF-8

当前实现完全符合这些要求。

## 常见的客户端设置

### JavaScript (fetch)
```javascript
fetch('/api/v1/ctdid/auth', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
})
```

### Python (requests)
```python
import requests
response = requests.post(
    'http://localhost:8000/api/v1/ctdid/auth',
    headers={'Content-Type': 'application/json'},
    json=data
)
```

### Java (OkHttp)
```java
RequestBody body = RequestBody.create(
    MediaType.parse("application/json"), 
    jsonString
);
Request request = new Request.Builder()
    .url("http://localhost:8000/api/v1/ctdid/auth")
    .post(body)
    .build();
```

如果问题仍然存在，请提供具体的错误日志和客户端代码，以便进一步排查。
