package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
)

// 国标库响应结构
type NationalAuthResponse struct {
	ResultCode string `json:"resultCode"`
	Data       string `json:"data"`
}

// 网络身份认证凭据数据结构（Base64解码后）
type PIDData struct {
	Version    string // 版本号 (1字节)
	NetworkID  string // 网络身份应用标识 (40字节)
	Timestamp  string // 时间戳 yyyymmddhhmmss (14字节)
	Signature  string // 签名值 (68-72字节)
}

func main() {
	// 您提供的响应数据
	responseJSON := `{"resultCode":"M000000", "data":"MIICKQYKKoEcz1UGAQQCA6CCAhkwggIVAgEAMYHQMIHNAgEAMD0wMTELMAkGA1UEBhMCQ04xETAPBgNVBAoTCEdMQ1RJRDAxMQ8wDQYDVQQDEwZHTENBMDECCF++xSx/G8lRMA0GCSqBHM9VAYItAwUABHoweAIgMDovDM/yT5HY+UvOqJig/INi/fNdYH4Y8q+6iPtDGuoCIHxgC06YGR2YRmHBfBxuxCQMw6zTkfxRCgGskXAni5ODBCD3idefNyyx3uajGlYJAbAgzvRf1N2pTopAzDOFtHWNZQQQiYLtrlotrGbfjcmNnPtkYzCCATsGCiqBHM9VBgEEAgEwCQYHKoEcz1UBaICCASBqmBHyMclQS5eVMgHPBtwP9NLlgPlMcgaVuaAfBr4HrIz2dhcx7FirLvCIScp5UqHcjBCOzV0E3vSnwFAdUQtF84Z+lajLz8z+nMXuEmVRF3YA8pxYvPui2m0dbT04i2+zb2WlaRZLz2hku6ZsXiDm+YO3vJJOKebOjC2scwzmYFLeWTKyVQxx70YO9/6A0PlblysMzGFrvS5hdW6YZSeqrccEAllKi1VEf4n8Rxe+1ehYM0iAn6NLcIzFql2GNQIJhQApwTD/DedzQe129Cz77RjQ4Z/QJ2jNWzmt4Kpkpph8LQ4JOAmcFRH3P+fiZ3Z+SI7NigpjYHV9NXKplQgSQJUEhiQSXr8aJKpqaKo3jOI1EmpCm15Vho/xxAA/6s0="}`

	fmt.Println("=== 国标库响应解码分析 ===")
	
	// 解析 JSON 响应
	var response NationalAuthResponse
	err := json.Unmarshal([]byte(responseJSON), &response)
	if err != nil {
		log.Fatalf("解析 JSON 失败: %v", err)
	}

	fmt.Printf("响应结果码: %s\n", response.ResultCode)
	
	// 检查结果码
	if response.ResultCode == "M000000" {
		fmt.Println("✅ 请求成功")
	} else {
		fmt.Printf("❌ 请求失败，结果码: %s\n", response.ResultCode)
	}

	fmt.Printf("Base64 数据长度: %d 字符\n", len(response.Data))
	fmt.Printf("Base64 数据前100字符: %s...\n", response.Data[:100])
	
	// Base64 解码
	decodedData, err := base64.StdEncoding.DecodeString(response.Data)
	if err != nil {
		log.Fatalf("Base64 解码失败: %v", err)
	}

	fmt.Printf("\n=== Base64 解码结果 ===\n")
	fmt.Printf("解码后数据长度: %d 字节\n", len(decodedData))
	fmt.Printf("解码后数据类型: 二进制数据（可能是加密的网络身份认证凭据）\n")
	
	// 打印十六进制格式
	fmt.Printf("\n=== 十六进制格式 ===\n")
	for i := 0; i < len(decodedData) && i < 200; i += 16 {
		end := i + 16
		if end > len(decodedData) {
			end = len(decodedData)
		}
		
		fmt.Printf("%04x: ", i)
		for j := i; j < end; j++ {
			fmt.Printf("%02x ", decodedData[j])
		}
		
		// 填充空格
		for j := end; j < i+16; j++ {
			fmt.Printf("   ")
		}
		
		// 打印可打印字符
		fmt.Printf(" |")
		for j := i; j < end; j++ {
			if decodedData[j] >= 32 && decodedData[j] <= 126 {
				fmt.Printf("%c", decodedData[j])
			} else {
				fmt.Printf(".")
			}
		}
		fmt.Printf("|\n")
	}
	
	if len(decodedData) > 200 {
		fmt.Printf("... (还有 %d 字节数据)\n", len(decodedData)-200)
	}

	// 尝试解析为文本
	fmt.Printf("\n=== 尝试文本解析 ===\n")
	textData := string(decodedData)
	if isPrintable(textData) {
		fmt.Printf("可能的文本内容: %s\n", textData)
	} else {
		fmt.Println("数据不是纯文本格式，可能是加密或二进制数据")
	}

	// 分析数据结构（根据国标文档）
	fmt.Printf("\n=== 数据结构分析 ===\n")
	if len(decodedData) >= 55 {
		fmt.Println("根据国标文档，网络身份认证凭据数据结构应该包含:")
		fmt.Println("- 版本号 (1字节): 第0字节")
		fmt.Println("- 网络身份应用标识 (40字节): 第1-40字节")
		fmt.Println("- 时间戳 (14字节): 第41-54字节")
		fmt.Println("- 签名值 (68-72字节): 第55-X字节")
		fmt.Printf("\n但当前数据长度为 %d 字节，可能是加密后的数据\n", len(decodedData))
	} else {
		fmt.Printf("数据长度 %d 字节，小于预期的最小长度 55 字节\n", len(decodedData))
	}

	// 检查是否是 ASN.1/DER 格式
	fmt.Printf("\n=== 格式检测 ===\n")
	if len(decodedData) > 0 {
		firstByte := decodedData[0]
		fmt.Printf("首字节: 0x%02x (%d)\n", firstByte, firstByte)
		
		if firstByte == 0x30 {
			fmt.Println("可能是 ASN.1/DER 编码的数据（SEQUENCE）")
		} else if firstByte == 0x04 {
			fmt.Println("可能是 ASN.1/DER 编码的 OCTET STRING")
		} else if firstByte >= 0x01 && firstByte <= 0x09 {
			fmt.Println("可能是版本号")
		} else {
			fmt.Printf("未知格式，首字节: 0x%02x\n", firstByte)
		}
	}

	fmt.Println("\n=== 总结 ===")
	fmt.Println("这是一个成功的国标库响应，包含加密的网络身份认证凭据数据。")
	fmt.Println("数据已经过加密处理，需要使用相应的解密算法才能获取原始的PID信息。")
}

// 检查字符串是否为可打印字符
func isPrintable(s string) bool {
	for _, r := range s {
		if r < 32 || r > 126 {
			return false
		}
	}
	return true
}
