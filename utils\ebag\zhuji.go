package ebag

import (
	"account/global"
	"encoding/json"
)

type FResponseData struct {
	ChildList json.RawMessage `json:"childList"`
	UserInfo  json.RawMessage `json:"userInfo"`
}

type ZhujiUserInfo struct {
	CardNo   string          `json:"cardNo"`
	Gender   string          `json:"gender"`
	Username string          `json:"username"`
	Mobile   string          `json:"mobile,omitempty"`
	School   string          `json:"school,omitempty"`
	RawData  json.RawMessage `json:"-"`
}

type ZhujiUserRespData struct {
	ChildList []ZhujiUserInfo
	UserInfo  ZhujiUserInfo
}

func GetZhujiUserInfo(ticket string) (data ZhujiUserRespData, err error) {
	res, err := SendJson(global.CONFIG.Ebag.Zhuji,
		map[string]interface{}{
			"ticket": ticket,
		})
	if err != nil {
		return
	}
	var resp FResponseData
	if err = json.Unmarshal(res, &resp); err != nil {
		return
	}
	data.UserInfo.RawData = make([]byte, len(resp.UserInfo))
	copy(data.UserInfo.RawData, resp.UserInfo)
	json.Unmarshal(resp.UserInfo, &data.UserInfo)
	var childlist []json.RawMessage
	json.Unmarshal(resp.ChildList, &childlist)
	for i := range childlist {
		var v ZhujiUserInfo
		v.RawData = make([]byte, len(childlist[i]))
		copy(v.RawData, childlist[i])
		json.Unmarshal(v.RawData, &v)
		data.ChildList = append(data.ChildList, v)
	}
	return
}
