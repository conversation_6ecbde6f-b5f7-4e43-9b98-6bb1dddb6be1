package zt

const (
	studentParentsUrl = "/api/v2/person/student/parents"
)

type (
	GetStudentParentsReq struct {
		StudentId string
	}
	GetStudentParentsRes struct {
		ParentList []Parent `json:"parent_list,omitempty"`
	}

	Parent struct {
		PersonID         string `json:"person_id"`
		Name             string `json:"name"`
		GenderName       string `json:"gender_name"`
		Phone            string `json:"phone"`
		RelationshipName string `json:"relationship_name"`
	}
)

func GetStudentParents(in *GetStudentParentsReq) (data GetStudentParentsRes, err error) {
	resp := Resp{Data: &data}
	_, err = client.R().SetQueryParam("student_id", in.StudentId).
		ForceContentType("application/json").
		SetResult(&resp).
		SetError(&resp).
		Get(studentParentsUrl)

	if err != nil {
		return
	}
	if err = checkResp(&resp); err != nil {
		return
	}
	return
}
