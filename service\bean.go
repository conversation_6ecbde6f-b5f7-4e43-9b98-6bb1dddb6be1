package service

import (
	"account/global"
	"account/model"
	"errors"
	"time"

	"gorm.io/gorm"
)

// BeanPay 学豆消费
func BeanPay(uid, amount int) bool {
	// return global.DB.Model(&model.UserStatus{}).Where("uid=? and bean>?", uid, amount).Update("bean", gorm.Expr("bean-?", amount)).RowsAffected > 0
	return global.DB.Exec("Update `user_status` Set `bean`=bean-? Where `uid`=? And `bean`>? Limit 1", amount, uid, amount).RowsAffected > 0
}

// BeanCharge 学豆充值
func BeanCharge(uid, amount int) bool {
	tx := global.DB.Model(&model.UserStatus{}).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	var s model.UserStatus
	err := tx.First(&s, "uid=?", uid).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return false
	}
	if err != nil {
		s.Uid = uid
		s.Bean = amount
		if err = tx.Create(&s).Error; err != nil {
			tx.Rollback()
			return false
		}
	} else {
		if err = tx.Where("uid=?", uid).Update("bean", s.Bean+amount).Error; err != nil {
			tx.Rollback()
			return false
		}
	}
	tx.Commit()
	return true
}

// CreateBeanlog 新增学豆记录
func CreateBeanlog(uid, amount, t int, info, appid string) (*model.Beanlog, error) {
	var blog model.Beanlog
	blog.Uid = uid
	blog.BidPrice = amount
	if t < 1 || t > 2 {
		t = 0
	}
	blog.BidType = t
	blog.BidInfo = info
	blog.Appid = appid
	blog.BidTime = time.Now().Unix()
	err := global.DB.Model(&model.Beanlog{}).Create(&blog).Error
	return &blog, err
}

// GetBeanLogRecord 获取学豆消费记录
func GetBeanLogRecord(uid int, start, end int64) (list []model.Beanlog, err error) {
	err = global.DB.Model(&model.Beanlog{}).Order("bid_time DESC").Find(&list, "uid=? and bid_time Between ? and ?", uid, start, end).Limit(1000).Error
	return
}
