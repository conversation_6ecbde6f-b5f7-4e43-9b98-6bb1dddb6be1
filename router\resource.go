package router

import (
	v1 "account/controller/v1"

	"github.com/gin-gonic/gin"
)

func InitResourceRouter(Router *gin.RouterGroup) {
	ApiRouter := Router.Group("resource")
	{
		ApiRouter.POST("finger", v1.ResourceFinger)
		ApiRouter.POST("fingers_v3", v1.ResourceFingersV3)
		ApiRouter.POST("fingers2", v1.ResourceFingers2)
		ApiRouter.POST("paper", v1.ResourcePaper)
		ApiRouter.POST("lostfinger", v1.ResourceLostFinger)
		ApiRouter.POST("pen", v1.ResourcePen)
		ApiRouter.POST("fingers", v1.ResourceFingers) // 【弃用】直接返回参数错误
	}
}
