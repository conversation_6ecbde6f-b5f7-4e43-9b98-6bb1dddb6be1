package utils

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: StructToMap
//@description: 利用反射将结构体转化为map
//@param: obj interface{}
//@return: map[string]interface{}
func StructToMap(obj interface{}) map[string]interface{} {
	obj1 := reflect.TypeOf(obj)
	obj2 := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < obj1.NumField(); i++ {
		data[obj1.Field(i).Name] = obj2.Field(i).Interface()
	}
	return data
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: ArrayToString
//@description: 将数组格式化为字符串
//@param: array []interface{}
//@return: string
func ArrayToString(array interface{}) string {
	return strings.Replace(strings.Trim(fmt.Sprint(array), "[]"), " ", ",", -1)
}

// IntId 把Float32,Float64和int64类型统一为int
func IntId(val interface{}) (Id int) {
	if val != nil {
		switch reflect.TypeOf(val).Kind() {
		case reflect.Int64:
			Id = int(val.(int64))
		case reflect.Int32:
			Id = int(val.(int32))
		case reflect.Float32:
			Id = int(val.(float32))
		case reflect.Float64:
			Id = int(val.(float64))
		case reflect.Int:
			Id = val.(int)
		case reflect.Uint:
			Id = int(val.(uint))
		}
	}
	return Id
}

//StructToLittleHumpMap 利用反射将结构体转化为小驼峰map
func StructToLittleHumpMap(obj interface{}) map[string]interface{} {
	obj1 := reflect.TypeOf(obj)
	obj2 := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < obj1.NumField(); i++ {
		data[LowerFirstLetter(obj1.Field(i).Name)] = obj2.Field(i).Interface()
	}
	return data
}

//LowerFirstLetter 字符首字母小写
func LowerFirstLetter(str string) string {
	var upperStr string
	vv := []rune(str) // 后文有介绍
	for i := 0; i < len(vv); i++ {
		if i == 0 {
			if vv[i] >= 65 && vv[i] <= 90 {
				vv[i] += 32 // string的码表相差32位
				upperStr += string(vv[i])
			} else {
				fmt.Println("Not begins with lowercase letter,")
				return str
			}
		} else {
			upperStr += string(vv[i])
		}
	}
	return upperStr
}

//FilterNull 过滤空字段,查询用
func FilterNull(inputData map[string]interface{}) (result map[string]interface{}) {
	for k, v := range inputData {
		deleteFlag := false
		switch reflect.TypeOf(v).Kind() {
		case reflect.String:
			if v.(string) == "" {
				deleteFlag = true
			}
		case reflect.Int, reflect.Int64, reflect.Int32, reflect.Float32, reflect.Float64:
			if IntId(v) == 0 {
				deleteFlag = true
			}
		case reflect.Slice, reflect.Array:
			s := reflect.ValueOf(v)
			if s.Len() <= 0 {
				deleteFlag = true
			}
		}
		if deleteFlag {
			delete(inputData, k)
		}
	}
	result = inputData
	return
}

func StructToUnderscoreMap(obj interface{}) map[string]interface{} {
	obj1 := reflect.TypeOf(obj)
	obj2 := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < obj1.NumField(); i++ {
		data[Underscore(obj1.Field(i).Name)] = obj2.Field(i).Interface()
	}
	return data
}

// Underscore 驼峰形式转下划线，如：UserID => user_id
func Underscore(str string) string {
	result := strings.Builder{}
	s := []rune(str)
	prefix := true
	for i := range s {
		if s[i] >= 65 && s[i] <= 90 {
			if !prefix {
				result.WriteByte('_')
			}
			result.WriteRune(s[i] + 32)
			prefix = true
		} else {
			result.WriteRune(s[i])
			prefix = false
		}
	}
	return result.String()
}

func SplitIntstr(s string, flag string) (list []int) {
	ss := strings.Split(s, flag)
	list = make([]int, 0, len(ss))
	for _, s := range ss {
		if x, err := strconv.Atoi(s); err == nil {
			list = append(list, x)
		}
	}
	return
}

func SplitInt64str(s string, flag string) (list []int64) {
	ss := strings.Split(s, flag)
	list = make([]int64, 0, len(ss))
	for _, s := range ss {
		if x, err := strconv.ParseInt(s, 10, 64); err == nil {
			list = append(list, x)
		}
	}
	return
}
