package service

import (
	"account/global"
	"account/model"
	"strconv"
	"time"

	"github.com/go-redis/redis"
)

// 记录某个个人资料编辑字段的编辑时间
func SetRedisProfileLimit(uid int, field string, timestamp int64) {
	if global.Redis == nil {
		return
	}
	key := "account:profile_limit:" + strconv.Itoa(uid)
	global.Redis.HSet(key, field, timestamp)
	global.Redis.Expire(key, time.Hour*24*30)
}

func GetRedisProfileLimit(uid int, field string) (int64, error) {
	if global.Redis == nil {
		return 0, redis.Nil
	}
	key := "account:profile_limit:" + strconv.Itoa(uid)
	return global.Redis.HGet(key, field).Int64()
}

func getKeyField(limitType int) string {
	switch limitType {
	case model.ProfileLimitRealname:
		return "realname"
	case model.ProfileLimitSchool:
		return "school"
	case model.ProfileLimitNickname:
		return "nickname"
	default:
		return ""
	}
}

func CreateProfileLimit(uid, t int) (reachLimit bool) {
	now := time.Now()
	field := getKeyField(t)
	if field == "" {
		return
	}
	reachLimit = true
	if t == model.ProfileLimitNickname {
		key := "account:nicknameEditCnt:" + strconv.Itoa(uid)
		cnt, _ := global.Redis.IncrBy(key, 1).Result()
		if cnt == 1 {
			global.Redis.Expire(key, time.Hour*24*30)
		}
		if cnt < 3 {
			reachLimit = false
		}
	}
	if reachLimit {
		SetRedisProfileLimit(uid, field, now.Unix())
	}
	global.DB.Table("profile_limit").Create(&model.ProfileLimit{Uid: uid, Type: t})
	return
}

// 判断个人资料中某个数据是否可编辑，返回距离可编辑的时间
func CheckProfileLimit(uid, t int, limit ...int) int {
	expire := int64(global.PROFILE_EDIT_LIMIT)
	now := time.Now()
	field := getKeyField(t)
	if field == "" {
		return 0
	}
	data, err := GetRedisProfileLimit(uid, field)
	if err == nil {
		if now.Unix()-data >= expire {
			return 0
		} else {
			return int(expire - (now.Unix() - data))
		}
	}
	if t == model.ProfileLimitNickname && checkNicknameLimit(uid) == 0 {
		return 0
	}
	var pl model.ProfileLimit
	global.DB.Table("profile_limit").
		Select("id, created_at").
		Where("uid = ? And `type` = ?", uid, t).
		Order("id DESC").
		Limit(1).Find(&pl)

	var result int64
	if pl.ID > 0 && now.Unix()-pl.CreatedAt.Unix() < expire {
		result = expire - (now.Unix() - pl.CreatedAt.Unix())
	}
	SetRedisProfileLimit(uid, field, result)
	return int(result)
}

// 检查是否达到每月3次修改限制
func checkNicknameLimit(uid int) int {
	key := "account:nicknameEditCnt:" + strconv.Itoa(uid)
	if global.Redis != nil {
		cnt, err := global.Redis.Get(key).Int()
		if err == nil && cnt < 3 {
			return 0
		}
	}
	var total int64
	global.DB.Table("profile_limit").
		Select("id, created_at").
		Where("uid = ? And `type` = ?", uid, model.ProfileLimitNickname).
		Where("created_at >= ?", time.Now().Add(-1*time.Second*global.PROFILE_EDIT_LIMIT)).Count(&total)
	if total < 3 && global.Redis != nil {
		global.Redis.SetNX(key, total, time.Second*global.PROFILE_EDIT_LIMIT)
		return 0
	}
	return int(total)
}
