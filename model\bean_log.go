package model

import "strconv"

type Beanlog struct {
	MlogId   int    `json:"mlog_id" gorm:"primarykey;autoIncrement"`
	Uid      int    `json:"uid"`
	Appid    string `json:"appid"`
	BidPrice int    `json:"bid_price"`
	BidType  int    `json:"bid_type" gorm:"default:0;commit:'1 入账；2 出账'"`
	BidInfo  string `json:"bid_info"`
	BidTime  int64  `json:"bid_time"`
}

func (Beanlog) TableName() string {
	return "user_beanlog"
}

func (b Beanlog) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"mlog_id":   strconv.Itoa(b.MlogId),
		"uid":       strconv.Itoa(b.Uid),
		"appid":     b.Appid,
		"bid_price": strconv.Itoa(b.BidPrice),
		"bid_type":  strconv.Itoa(b.BidType),
		"bid_info":  b.<PERSON><PERSON>In<PERSON>,
		"bid_time":  strconv.FormatInt(b.<PERSON>id<PERSON>, 10),
	}
}
