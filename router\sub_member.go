package router

import (
	v1 "account/controller/v1"

	"github.com/gin-gonic/gin"
)

func InitSubmemberRouter(Router *gin.RouterGroup) {
	ApiRouter := Router.Group("submember")
	{
		ApiRouter.POST("add", v1.SubmemberAdd)
		Any(ApiRouter, "list", v1.SubmemberList)
		Any(ApiRouter, "login", v1.SubmemberLogin)
		Any(ApiRouter, "remove", v1.SubmemberRemove)
		Any(ApiRouter, "reorder", v1.SubmemberReorder)
		ApiRouter.GET("simple/list", v1.SubmemberSimpleList)
	}
}
