package v1

import (
	"account/global"
	"account/model/response"
	"account/service"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// ActivateIndex 邮箱激活
// login no
// @Param sn string true "签名"
// @Param appid string true "应用id"
// @Param email string true "邮箱"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Success {html}
// @Router /activate [get]
func ActivateIndex(c *gin.Context) {
	reason, _, _ := checkSignature(c, global.EMAIL_VAILD, global.SystemApps)
	appid := GetRequestParam(c, "appid")
	redirect := (appid == "dream.cn")
	if reason < 0 {
		renderHTML(c, "很抱歉，您无法完成此操作！", redirect, "", "",
			"失败原因", "连接已过期",
			"提示", "该连接已过期，无法继续访问，如有疑问请联系客服，谢谢。",
		)
		return
	}
	email := GetRequestParam(c, "email")
	m, _ := service.GetMemberByEmail(email)
	if m.Uid == 0 {
		renderHTML(c, "很抱歉，您无法完成此操作！", redirect, "", "",
			"失败原因", "账号不存在",
			"提示", "很抱歉，我们无法找到该帐号，可能未注册或已被删除，如有疑问请联系客服，谢谢。",
		)
		return
	}
	if m.Forbidden == 1 {
		renderHTML(c, "很抱歉，您无法完成此操作！", redirect, "", "",
			"账号", m.Username,
			"邮箱", email,
			"失败原因", "账号被禁用",
			"提示", "该帐号已被禁止使用，如有疑问请联系客服，谢谢。",
		)
		return
	}
	if m.Forbidden != 4 {
		renderHTML(c, "很抱歉，该帐号已激活", redirect, "", email,
			"账号", m.Username,
			"邮箱", email,
			"账号状态", "已激活",
			"提示", "非常抱歉，该帐号已经激活过了，无需再次激活，如有疑问请联系客服，谢谢。",
		)
		return
	}
	code, msg := activateCheckVerify(c, email)
	if code != 0 {
		renderHTML(c, msg, redirect, "", email,
			"账号", m.Username,
			"邮箱", email,
			"失败原因", msg,
			"提示", "您访问的连接可能已经失效，无法继续访问，如有疑问请联系客服，谢谢。",
		)
		return
	}
	if err := service.SetMemberForbidden(int(m.Uid), 0); err != nil {
		renderHTML(c, "很抱歉，您无法完成此操作！", redirect, "", email,
			"账号", m.Username,
			"邮箱", email,
			"失败原因", "系统内部错误",
			"提示", "服务器出问题了，我们正在抢修中，请稍后再访问。",
		)
	} else {
		renderHTML(c, "恭喜您，帐号激活成功！", redirect, "success", email,
			"账号", m.Username,
			"邮箱", email,
			"账号状态", "已激活",
			"提示", "您可以在读书郎所有产品中使用该帐号，请妥善保管好您的密码，感谢您的使用。",
		)
	}
}

// renderHTML
// @param infos 提示内容，（名称，详情）字符串组
func renderHTML(c *gin.Context, message string, redirect bool, status string, email string, infos ...string) {
	if redirect {
		url := fmt.Sprintf(`http://account.dream.cn/register/activated?status=%s&message=%s`, status, url.QueryEscape(message))
		if email != "" {
			url += "&email=" + email
		}
		c.Redirect(302, url)
		return
	}
	title := "操作成功-读书郎帐号中心"
	if status != "success" {
		title = "出错了-读书郎帐号中心"
		status = "failed"
	}
	builder := strings.Builder{}
	builder.WriteString(`<!DOCTYPE html><html>
	<head><meta http-equiv='Content-Type' content='text/html;charset=utf-8'><meta name='viewport' content='width=device-width,minimum-scale=1.0,maximum-scale=1.0,initial-scale=1.0,user-scalable=no' />
	<style>*{margin:0;padding:0;}body{font-family:'Microsoft Yahei',tahoma,sans-serif;color:#333}
	h3{padding: 1em 0.5em} p,h5{padding:1em 0.5em 0} h5{color:#666;font-weight:500} .m2{margin-top:2em}hr{border-style:none;border-top:1px solid #A00;}
	.success{color:#080;border-color:#080;background-color:#EFE;}.failed{color:#A00;border-color:#A00;background-color:#FEE;}
	</style>`)
	builder.WriteString(`<title>` + title + `</title></head><body><h3 class="` + status + `">` + message + `</h3>`)
	if n := len(infos); n > 1 {
		builder.WriteString(`<hr class="` + status + `" />`)
		if n%2 != 0 {
			n--
		}
		for i := 0; i < n; i += 2 {
			builder.WriteString(`<p><b>` + infos[i] + `</b>:` + infos[i+1] + `</p>`)
		}
	}
	builder.WriteString(`<h5 align="right" class="m2">` + time.Now().Format(global.TIME_LAYOUT_DATE) + `</h5>`)
	builder.WriteString(`<h5 align="right">读书郎教育科技有限公司</h5></body></html>`)
	c.Data(200, "text/html;charset=utf8", []byte(builder.String()))
}

// activateCheckVerify 激活验证码检查
func activateCheckVerify(c *gin.Context, email string) (code int, msg string) {
	serial := GetRequestParam(c, "serial")
	verify := GetRequestParam(c, "verify")
	if serial == "" {
		code = response.E_INVALID_SERIAL
		msg = response.Errmsg(&code)
		return
	}
	if verify == "" {
		code = response.E_INVALID_VERIFY
		msg = response.Errmsg(&code)
		return
	}
	v, err := service.GetVerifyById(serial)
	if err != nil {
		return response.E_VERIFY, "验证码错误或已过期"
	}
	if v.Status != 1 {
		code = response.E_INVALID_SERIAL
		msg = response.Errmsg(&code)
		return
	}
	if v.Username != email {
		code = response.E_ACCOUNT_MISMATCH
		msg = response.Errmsg(&code)
		return
	}
	if v.Verify != verify {
		code = response.E_VERIFY
		msg = response.Errmsg(&code)
		return
	}
	service.SetVerifyStatus(v.ID, 2)
	if v.Expire <= time.Now().Unix() {
		code = response.E_SERIAL_EXPIRED
		msg = response.Errmsg(&code)
		return
	}
	return 0, ""
}
