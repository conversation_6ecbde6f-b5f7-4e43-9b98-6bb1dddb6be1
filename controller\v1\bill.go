package v1

import (
	"account/global"
	"account/model/request"
	"account/model/response"
	"account/service"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BillUpload 账单上传
// @Param data json:[]request.BillItem true
// @Success {json} json {"success": true}
// @Router /bill/upload [post]
func BillUpload(c *gin.Context) {
	data := GetRequestParam(c, "data")
	if data == "" {
		response.OkWithData(gin.H{"success": false}, c)
		return
	}
	var list []map[string]interface{}
	if err := json.Unmarshal([]byte(data), &list); err != nil {
		response.OkWithData(gin.H{"success": false, "err": err.Error()}, c)
		return
	}
	for i := range list {
		bill := request.BillItem{
			ID:     mapFieldToString(list[i], "id"),
			Date:   mapFieldToString(list[i], "date"),
			Model:  mapFieldToString(list[i], "model"),
			Qty:    mapFieldToString(list[i], "qty"),
			Dealer: mapFieldToString(list[i], "dealer"),
			Typeid: mapFieldToString(list[i], "typeid"),
		}
		addBill(bill)
	}
	response.OkWithData(gin.H{"success": true}, c)
}

func mapFieldToString(m map[string]interface{}, field string) string {
	if data, ok := m[field]; ok {
		return fmt.Sprint(data)
	} else {
		return ""
	}
}

func modelFilter(model string) string {
	switch model {
	case "W2T":
		return "W2S"
	case "W7":
		return "A3"
	default:
		return model
	}
}

func addBill(bill request.BillItem) {
	if bill.Model == "" {
		return
	}
	b, err := service.GetBillById(bill.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			bData := gin.H{
				"id":         bill.ID,
				"date":       bill.Date,
				"model":      modelFilter(bill.Model),
				"qty":        bill.Qty,
				"lastupdate": time.Now(),
			}
			if bill.Dealer != "" && bill.Typeid != "" {
				bData["dealer_id"] = checkDealer(bill.Typeid, bill.Dealer)
			} else {
				bData["dealer_id"] = 0
			}
			service.CreateBillByMap(bData)
		}
		return
	}
	b.Model = modelFilter(bill.Model)
	if t, err := time.Parse(global.TIME_LAYOUT_DATE, bill.Date); err == nil {
		b.Date = t
	}
	b.Qty = bill.Qty
	b.Lastupdate = time.Now()
	if bill.Dealer != "" && bill.Typeid != "" {
		b.DealerId = strconv.Itoa(checkDealer(bill.Typeid, bill.Dealer))
	}
	service.SaveBill(b)
}

func checkDealer(customer_number, dealer string) int {
	d, _ := service.GetDealerByCustomerNumber(customer_number)
	if d.ID == 0 {
		d.CustomerNumber = customer_number
		d.Dealer = dealer
		service.CreateDealer(d)
	} else if d.Dealer != dealer {
		d.Dealer = dealer
		service.SaveDealer(d)
	}
	return d.ID
}
