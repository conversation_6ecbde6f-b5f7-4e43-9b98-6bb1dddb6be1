package request

type ZtLoginReq struct {
	ZtId       string `json:"zt_id" form:"zt_id" binding:"gt=0"`
	Password   string `json:"password" form:"password"`
	Nickname   string `binding:"omitempty,min=2,max=60" json:"nickname" form:"nickname"`
	Gender     uint   `binding:"omitempty,oneof=0 1" json:"gender" form:"gender"`
	Birthday   string `json:"birthday" form:"birthday"` // 生日，格式2006-01-02
	Grade      int    `json:"grade" form:"grade"`
	School     string `binding:"omitempty,min=2,max=100" json:"school" form:"school"`
	ProvinceId string `form:"province_id" json:"province_id"`
	CityId     string `form:"city_id" json:"city_id"`
	DistrictId string `form:"district_id" json:"district_id"`
	Mobile     string `json:"-" form:"-"` // 家长手机号。从中台接口查询
	Ip         string `json:"-" form:"-"` // 注册ip
	Regapp     string `json:"-" form:"-"` // 注册应用
}

type ZtCheckReq struct {
	Uid  uint   `json:"uid" form:"uid"`
	ZtId string `json:"zt_id" form:"zt_id"`
}
