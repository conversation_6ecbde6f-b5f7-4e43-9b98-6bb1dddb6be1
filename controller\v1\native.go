package v1

import (
	"account/global"
	"account/model"
	"account/model/request"
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var native_apps = map[string]string{
	"com.readboy.nativeservice": "2e0b8b8ed59178e20a73ca71a5dcaa18",
	"api1-yx.readboy.com":       "e615a029d74712ca8bfc507c30a515d8", // 黄卓辉
	"com.readboy.post_repair":   "9938ab8e2ba870179aba8067665c2391", //寄修系统
	"listen-speak.readboy.com":  "7ad6fd70fcf6f3ab565ccefbc906d4a7", // 谢坤-小郎听说（内部注册）
	"zxs-native":                "27637b67c49a89aba84813d708d9b444", // 陈成德-智习室（内部注册）
}

// NativeRegister 内部注册
// login no
// @Param sn string true "签名"
// @Param mobile string true "手机"
// @Param password string true "密码md5"
// @Param nickname string true "昵称"
// @Success 200 {json} json {"uid": "", "username": "", "forbidden": "", "access_token": "", "access_expire": **********, "avatar": "", "nickname": ""}
// @Router /native/register [post]
func NativeRegister(c *gin.Context) {
	reason, desp, _ := checkSignature(c, global.SN_EXPIRE, native_apps)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	m, err := service.GetMemberByMobile(mobile)
	var p *model.UserProfile
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			m.Username = mobile
			password := GetRequestParam(c, "password")
			if password == "" {
				password = strconv.Itoa(rand.Intn(900000) + 100000)
			}
			service.FormatMemberPassword(m, password)
			now := time.Now().Unix()
			timestr := strconv.FormatInt(now, 10)
			m.AccessToken = utils.MD5(mobile + timestr)
			m.AccessExpire = now + global.TOKEN_EXPIRE
			m.Regip = GetIp(c)
			m.Regdate = now
			m.Regapp = GetAppid(c)
			m.Forbidden = 0
			m.Mobile = mobile
			err = service.CreateMember(m)
			p = &model.UserProfile{}
			p.Uid = int(m.Uid)
			p.Username = mobile
			p.Realname = GetRequestParam(c, "nickname")
			service.CreateProfile(p)
		}
		if err != nil {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
	}
	data := getMemberLoginInfo(m)
	data["avatar"] = utils.GetAvatarUri(m.Uid)
	if p == nil {
		p, _ = service.GetProfileByUid(m.Uid)
	}
	data["nickname"] = p.Realname
	response.OkWithData(data, c)
}

// NativeSearchMobile 通过手机号查找用户
// login no
// @Param sn string true "签名"
// @Param key string true "手机号数组（len < 100）" "1,2,3"
// @Success 200 {json} json {"result": [{"uid": "", "username": "", "mobile": "", "regdate": "", "regip": ""}]}
// @Router /native/searchMobile [get]
func NativeSearchMobile(c *gin.Context) {
	reason, desp, _ := checkSignature(c, global.SN_EXPIRE, native_apps)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	key := GetRequestParam(c, "key")
	if ok, _ := regexp.Match(`^([\d]+,)*([\d]+)$`, []byte(key)); !ok {
		response.FailWithErrcode(response.E_PARAM, "参数非法", c)
		return
	}
	if len(strings.Split(key, ",")) > 100 {
		response.FailWithErrcode(response.E_PARAM, "参数越界", c)
		return
	}
	list, _ := service.GetMemberByMobiles(key)
	data := make([]gin.H, 0, len(list))
	uids := make([]uint, 0, len(list))
	for _, v := range list {
		uids = append(uids, v.Uid)
		ret := gin.H{
			"uid":       strconv.Itoa(int(v.Uid)),
			"username":  v.Username,
			"mobile":    v.Mobile,
			"regdate":   time.Unix(v.Regdate, 0).Format(global.TIME_LAYOUT_DATECLOCK),
			"regip":     v.Regip,
			"forbidden": strconv.Itoa(v.Forbidden),
		}
		data = append(data, ret)
	}
	profiles, _ := service.GetProfileMapByUids(uids)
	for i := range data {
		data[i]["realname"] = profiles[list[i].Uid].Realname
	}
	response.OkWithData(gin.H{"data": data}, c)
}

func NativeSearchUid(c *gin.Context) {
	reason, desp, _ := checkSignature(c, global.SN_EXPIRE, native_apps)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	uid := GetRequestParam(c, "key")
	if uid == "" || uid == "0" {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	m, err := service.GetMemberByUid(uid)
	if err != nil {
		response.FailWithErrcode(response.E_UNKNOWN, "", c)
		return
	}
	response.OkWithData(map[string]interface{}{
		"weixin": m.Unionid,
	}, c)
}

// NativeBindDeviceUser 绑定机器登录账号
// @Router /native/deviceBind [post]
func NativeBindDeviceMember(c *gin.Context) {
	reason, desp, _ := checkSignature(c, global.SN_EXPIRE, native_apps)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	var req request.NativeDeviceBindMemberReq
	if err := c.ShouldBind(&req); err != nil {
		response.FailWithError(response.New(response.E_PARAM, "", err), c)
		return
	}
	dm := model.DeviceMember{
		Serial: req.Serial,
		Uid:    uint(req.Uid),
	}
	err := service.BindDeviceMember(&dm)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	if dm.ID == 0 {
		response.FailWithErrcode(response.E_ALREADY_BOUND, "", c)
		return
	}
	response.OkWithData(gin.H{
		"status": 1,
	}, c)
}

// NativeDevicesFace 获取一对一人脸数据
// @Router /native/devicesFace [post]
func NativeDevicesFace(c *gin.Context) {
	reason, desp, _ := checkSignature(c, global.SN_EXPIRE, native_apps)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	var req request.NativeDevicesFaceReq
	if c.ShouldBind(&req) != nil {
		response.FailWithErrcode(response.E_PARAM, "序列号不能为空", c)
		return
	}
	data, err := service.GetNativeDevicesFace(req.Serial)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{
		"data": data,
	}, c)
}

// NativeUserFace 获取人脸数据
// @Router /native/userFace [get]
func NativeUserFace(c *gin.Context) {
	reason, desp, _ := checkSignature(c, global.SN_EXPIRE, native_apps)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	var req request.NativeUserFaceReq
	if err := c.ShouldBind(&req); err != nil {
		response.FailWithError(response.New(response.E_PARAM, "", err), c)
		return
	}
	data, err := service.GetNativeUserFace(&req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{
		"data": data,
	}, c)
}

// NativeDeleteUserFace 删除人脸数据
// @Router /native/deleteUserFace [post]
func NativeDeleteUserFace(c *gin.Context) {
	reason, desp, _ := checkSignature(c, global.SN_EXPIRE, native_apps)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	idstr := GetRequestParam(c, "ids")
	ids := utils.SplitInt64str(idstr, ",")
	if len(ids) == 0 {
		response.FailWithErrcode(response.E_PARAM, "id不能为空", c)
		return
	}
	err := service.DeleteUserFaceById(ids)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{
		"status": 1,
	}, c)
}

// NativeUnbindDeviceMember 解绑机器登录账号
// @Router /native/deviceUnbind [post]
func NativeUnbindDeviceMember(c *gin.Context) {
	reason, desp, _ := checkSignature(c, global.SN_EXPIRE, native_apps)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	var req request.NativeUnbindDeviceMemberReq
	if err := c.ShouldBind(&req); err != nil {
		response.FailWithError(response.New(response.E_PARAM, "", err), c)
		return
	}
	err := service.UnbindDeviceMember(req.Serial, uint(req.Uid))
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{
		"status": 1,
	}, c)
}
