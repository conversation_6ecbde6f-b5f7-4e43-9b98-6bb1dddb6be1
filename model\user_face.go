package model

import (
	"time"

	"gorm.io/gorm"
)

type UserFace struct {
	ID        uint64         `json:"-"`
	Uid       uint           `json:"uid"`
	Serial    string         `json:"-"`         // 序列号
	Model     string         `json:"-"`         // 机型
	FaceId    string         `json:"face_id"`   // uuidV4
	ImageUrl  string         `json:"image_url"` // 图片url
	FaceUrl   string         `json:"face_url"`  // 人脸特征
	CreatedAt time.Time      `json:"-"`
	DeletedAt gorm.DeletedAt `json:"-"`
}

func (UserFace) TableName() string {
	return "user_face"
}
