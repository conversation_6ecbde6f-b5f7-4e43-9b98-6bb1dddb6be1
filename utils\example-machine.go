package utils

import (
	"account/global"
	"encoding/json"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

/*
*	样机认证
*	文档：http://showdoc.readboy.com/web/#/14?page_id=39
*	密码：care999
*	app_id :ac_center.readboy.com
*	app_key:318avb82704954028802c9325b12adsg
* */

// ExampleMachineLogin 样机登录
func ExampleMachineLogin(number, username string) (msg string, err error) {
	now := time.Now().Unix()
	nowstr := strconv.FormatInt(now, 10)
	appkey := "318avb82704954028802c9325b12adsg"
	deviceId := "Readboy_AccountCenter/1/ac_center.readboy.com/1"
	data := url.Values{}
	data.Set("device_id", deviceId)
	data.Set("t", nowstr)
	data.Set("sn", MD5(deviceId+appkey+nowstr))
	data.Set("number", number)
	data.Set("user_info", username)
	resp, err := SendPostForm("https://care.readboy.com/api/prototype/login", data)
	if err != nil {
		return
	}
	type ret struct {
		Errno  int    `json:"errno"`
		Errmsg string `json:"msg"`
	}
	var res ret
	err = json.Unmarshal(resp, &res)
	if res.Errno == 7015 {
		msg = res.Errmsg
		fd, err1 := os.OpenFile("/data/logs/Yang-"+time.Now().Format("2006-01")+".txt", os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0777)
		if err1 != nil {
			return
		}
		defer fd.Close()
		fd.WriteString(time.Now().Format(global.TIME_LAYOUT_DATECLOCK) + "<<<#" + strings.TrimRight(string(resp), "\r\n") + "#>>>\r\n")
	}
	return
}

// 检查是否为样机
func IsExampleMachine(number string) bool {
	now := time.Now().Unix()
	nowstr := strconv.FormatInt(now, 10)
	appkey := "318avb82704954028802c9325b12adsg"
	deviceId := "Readboy_AccountCenter/1/ac_center.readboy.com/1"
	data := url.Values{}
	data.Set("device_id", deviceId)
	data.Set("t", nowstr)
	data.Set("sn", MD5(deviceId+appkey+nowstr))
	data.Set("number", number)
	resp, err := SendGet("https://care.readboy.com/api/prototype/check?" + data.Encode())
	if err != nil {
		return false
	}
	type ret struct {
		OK int `json:"ok"`
	}
	var res ret
	json.Unmarshal(resp, &res)

	return res.OK == 1
}
