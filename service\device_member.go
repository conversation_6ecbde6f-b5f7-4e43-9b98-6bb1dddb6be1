package service

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/utils"
	"errors"
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func GetDeviceMember(serial string, uid uint) (data model.DeviceMember, err error) {
	err = global.DB.Model(&data).Where("serial = ?", serial).
		Order(clause.OrderBy{
			Expression: clause.Expr{SQL: "uid = ? DESC", Vars: []interface{}{uid}},
		}).
		Take(&data).Error
	return
}

func BindDeviceMember(dm *model.DeviceMember) (err error) {
	if dm == nil || dm.Serial == "" || dm.Uid == 0 {
		err = response.New(response.E_PARAM, "", nil)
		return
	}
	d, _ := GetDeviceMember(dm.Serial, dm.Uid)
	if d.ID > 0 {
		if d.Uid > 0 {
			if d.Uid == dm.Uid {
			} else {
				err = response.New(response.E_ALREADY_BOUND, "此机器已被绑定", nil)
			}
			return
		} else {
			err = global.DB.Model(&model.DeviceMember{}).Where("id = ?", d.ID).
				Updates(map[string]interface{}{
					"uid": dm.Uid,
				}).Error
			if err != nil {
				err = response.New(response.E_DATABASE, "", err)
				return
			}
		}
	}

	err = global.DB.Model(&dm).Clauses(clause.OnConflict{DoNothing: true}).Create(&dm).Error
	return
}

func UnbindDeviceMember(serial string, uid uint) (err error) {
	db := global.DB.Model(&model.DeviceMember{}).Where("serial = ?", serial)
	if uid > 0 {
		db = db.Where("uid = ?", uid)
	} else if !utils.IsExampleMachine(serial) {
		err = response.New(response.E_PARAM, "uid不能为空", nil)
		return
	}
	err = db.Delete(&map[string]interface{}{}).Error
	if err != nil {
		err = response.New(response.E_DATABASE, "", err)
		return
	}
	return
}

// BindDeviceMember2 绑定设备和成员
// 参数:
//
//	dm - 设备成员信息
//	maxMember - 设备可绑定的最大成员数
func BindDeviceMember2(dm *model.DeviceMember, maxMember int) (err error) {
	// 检查传入的设备成员信息是否有效
	if dm == nil || dm.Uid <= 0 || dm.Serial == "" {
		return
	}

	// 是否已绑定
	dd, err1 := GetDeviceMember(dm.Serial, dm.Uid)
	if err1 != nil && !errors.Is(err1, gorm.ErrRecordNotFound) {
		err = response.New(response.E_DATABASE, "", err1)
		return
	}
	// 如果找到相同的记录，说明已经绑定，直接返回
	if dd.Uid == dm.Uid {
		return
	}

	// 检查账号是否绑定其他设备
	var ud model.DeviceMember
	global.DB.Model(&model.DeviceMember{}).Where("uid = ? And serial != ?", dm.Uid, dm.Serial).Select("id").Take(&ud)
	if ud.ID > 0 {
		err = response.New(response.E_UNAMEPWD, "", nil)
		return
	}

	// 检查设备绑定的账号数量
	var dcnt int64
	global.DB.Model(&model.DeviceMember{}).Where("serial = ?", dm.Serial).Count(&dcnt)
	if dcnt >= int64(maxMember) {
		err = response.New(response.E_ALREADY_BOUND, "此设备已绑定账号数已达上限", nil)
		return
	}

	err = global.DB.Model(&dm).Clauses(clause.OnConflict{DoNothing: true}).Create(&dm).Error
	return
}

func GetDeviceMemberList(serial string, dModel string) (data response.DeviceMemberRes, err error) {
	data.MaxMember = 5 // TODO 后续按机型可能有不同的绑定数量
	var list []struct {
		Mobile string
		model.UserProfile
	}
	err = global.DB.Table("ac_device_member dm").
		Where("dm.serial = ? And dm.deleted_at is Null", serial).
		Joins("Left Join ac_members m on dm.uid = m.uid").
		Joins("Left Join user_profile p on dm.uid = p.uid").
		Select("dm.uid, m.username, m.mobile, m.realname, p.gender, p.birthday, p.school, p.grade").
		Limit(data.MaxMember).Order("dm.id DESC").
		Find(&list).Error
	if err != nil {
		err = response.New(response.E_DATABASE, "", err)
		return
	}
	for i := range list {
		data.List = append(data.List, response.DeviceMember{
			Uid:      uint(list[i].Uid),
			Username: list[i].Username,
			Mobile:   list[i].Mobile,
			Avatar:   utils.GetAvatarUri(uint(list[i].Uid)),
			Realname: list[i].Realname,
			Gender:   list[i].Gender,
			Birthday: fmt.Sprintf("%d-%02d-%02d", list[i].BirthY, list[i].BirthM, list[i].BirthD),
			Grade:    list[i].Grade,
			School:   list[i].School,
		})
	}
	return
}

func CheckDeviceMemberFree(serial string) (free bool) {
	var dm model.DeviceMember
	global.DB.Table("ac_device_member_free").Where("serial in (?) And deleted_at is null", []string{"*", serial}).Take(&dm)
	return dm.ID > 0
}
