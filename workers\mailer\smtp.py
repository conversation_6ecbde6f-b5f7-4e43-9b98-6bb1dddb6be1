#-*- coding:utf-8 -*-
from email.mime.text import MIMEText
from email.header import Header
import smtplib
import sys
import datetime

def get_provider(appid):
	if 'dream' in appid:
		return '追梦科技';
	return 'Readboy Account Center';
    
email_subject = [
    '帐号激活',
	'密码重置',
	'验证码']

def email_activate(appid, username, expire, activateurl):
	date = datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
	provider = get_provider(appid)
	
	body = f"<div style=\"border:2px solid #DDD\"><div><div style=\"padding:14px 0 0 14px;\"></div></div><div style=\"margin:2px 10px;\">尊敬的<span style=\"color:#0080CC\">{username}</span>，您好！<p>感谢您使用{provider}<p>请点击下面的链接激活您的帐号，完成注册。<p><a style=\"color:#00C0FF\" href=\"{activateurl}\" target=\"_blank\">{activateurl}</a><br><br>(如果点击链接没反应，请复制激活链接，粘帖到浏览器地址栏后访问)<ul style=\"color:#666\"><li>激活链接<b>{expire}</b>小时内有效， 超时请重新发送激活邮件。</li><li>激活链接将在您激活一次后失效。</li></ul></p><p><div style=\"float:right\"><p>{provider}<br>{date}</p></div><hr style=\"border-style:none;border-top:1px dotted #CCC;clear:both;\"><font color=\"gray\">如您错误收到此邮件，请不要激活链接，该帐户将不会启用<br>这是系统自动发出，请不要回复，如有疑问，请联系：</font><font color=\"#0080CC\">800-999-3685</font></p></div></div>";
	return body

def email_lost_password(appid, username, expire, verify, serial):
	date = datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
	provider = get_provider(appid)
	body = f"<div style=\"border:2px solid #DDD\"><div><div style=\"padding:14px 0 0 14px;\"></div></div><div style=\"margin:2px 10px;\">尊敬的<span style=\"color:#0080CC\">{username}</span>，您好！<p>感谢您使用{provider}<br><span>您的密码找回验证码为：<em style=\"color:#C40\">{verify}</em>, 序列号为：<em style=\"color:#333\">{serial}</em>,请尽快重置您的密码！</span><br><br><ul style=\"color:#666\"><li>重置密码<b>{expire}</b>小时内有效， 超时请重新发送找回密码邮件。</li><li>验证码将在您使用后失效。</li></ul></p><p><div style=\"float:right\"><p>{provider}<br>{date}</p></div><hr style=\"border-style:none;border-top:1px dotted #CCC;clear:both;\"><font color=\"gray\">如您错误收到此邮件，请忽略。<br>这是系统自动发出，请不要回复，如有疑问，请联系：</font><font color=\"#0080CC\">800-999-3685</font></p></div></div>";
	return body

def email_verify(appid, username, expire, verify, serial):
	date = datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
	provider = get_provider(appid)
	body = f"<div style=\"border:2px solid #DDD\"><div><div style=\"padding:14px 0 0 14px;\"></div></div><div style=\"margin:2px 10px;\">尊敬的<span style=\"color:#0080CC\">{username}</span>，您好！<p>感谢您使用{provider}<br><span>您的邮箱验证码为：<em style=\"color:#C40\">{verify}</em>, 序列号为：<em style=\"color:#333\">{serial}</em>,请尽快完成验证操作！</span><br><br><ul style=\"color:#666\"><li>验证<b>{expire}</b>小时内有效， 超时请重新发送验证邮件。</li><li>验证码将在您使用后失效。</li></ul></p><p><div style=\"float:right\"><p>{provider}<br>{date}</p></div><hr style=\"border-style:none;border-top:1px dotted #CCC;clear:both;\"><font color=\"gray\">如您错误收到此邮件，请忽略。<br>这是系统自动发出，请不要回复，如有疑问，请联系：</font><font color=\"#0080CC\">800-999-3685</font></p></div></div>";
	return body
    
from_addr = '<EMAIL>' #发件邮箱
password = 'Hello1234'#邮箱密码(或者客户端授权码)
smtp_server = 'smtp.readboy.com'#企业邮箱地址，若是个人邮箱地址为：smtp.163.com	

subject_id = int(sys.argv[1])
subject = email_subject[subject_id - 1]
appid = sys.argv[2]
username   = sys.argv[3]
expire     = sys.argv[4]
print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), sys.argv)
if(subject_id == 1):
    activate_url = sys.argv[5]
    message  = email_activate(appid, username, expire, activate_url)
if(subject_id == 2):
    verify  = sys.argv[5]
    serial = sys.argv[6]
    message  = email_lost_password(appid, username, expire, verify, serial)
if(subject_id == 3):
    verify  = sys.argv[5]
    serial = sys.argv[6]
    message  = email_verify(appid, username, expire, verify, serial)


msg = MIMEText(message,'html','utf-8')

msg['Subject'] = Header(subject,'utf-8')
msg['From'] = Header('<EMAIL>','utf-8')
msg['To'] = Header(username,'utf-8')

try:
    server = smtplib.SMTP_SSL(smtp_server,465)
    server.login(from_addr,password)
    server.sendmail(from_addr,username,msg.as_string())
    server.quit()
    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), subject, appid, username, "Success")
except smtplib.SMTPException as e:
    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), subject, appid, username, e)