# 网络身份认证 TLS 客户端证书配置指南

## 概述

国标网络身份认证接口需要使用 TLS 双向认证，即客户端需要提供证书进行身份验证。本系统已配置为自动从固定路径读取证书文件。

## 证书文件准备

需要准备以下证书文件，并放置在指定目录：

**固定证书目录**: `C:\读书郎教育科技有限公司\`

**必需文件**:
1. **cert.pem** - PEM 格式的客户端证书
2. **prikey.txt** - 客户端私钥文件  
3. **downstream_root.cer** - 服务器端的 CA 根证书

## 配置方法

### 1. 创建证书目录

```cmd
mkdir "C:\读书郎教育科技有限公司"
```

### 2. 放置证书文件

将您的证书文件复制到该目录：

```
C:\读书郎教育科技有限公司\
├── cert.pem                 # 客户端证书
├── prikey.txt              # 客户端私钥
└── downstream_root.cer     # CA根证书
```

### 3. 配置文件

系统会自动从固定路径读取证书，无需额外配置。在 `config.yaml` 中只需配置接口地址：

```yaml
ctdid:
  # 测试环境接口地址
  test_urls:
    - "https://1.30.133.75:20002/envelopData"
  
  # 请求超时时间（秒）
  timeout: 30
  
  # 重试次数
  retry_count: 1
```

## 测试配置

### 1. 使用测试接口

启动应用后，可以通过以下接口测试 TLS 配置：

```bash
GET /api/v1/ctdid/test-tls
```

### 2. 测试响应

成功配置时的响应：
```json
{
  "message": "TLS连接测试成功",
  "status": "ok"
}
```

失败时的响应：
```json
{
  "errno": 7008,
  "errmsg": "TLS连接测试失败: 具体错误信息"
}
```

## 工作原理

系统会自动：

1. **尝试加载证书**: 从固定路径 `C:\读书郎教育科技有限公司\` 加载证书文件
2. **容错处理**: 如果证书加载失败，会显示警告但不会中断连接
3. **智能验证**: 
   - 有CA证书时启用SSL验证
   - 无CA证书时跳过SSL验证（显示警告）
4. **日志输出**: 详细的加载状态和错误信息

## 日志示例

成功加载证书时的日志：
```
已加载客户端证书: C:\读书郎教育科技有限公司\cert.pem
已加载CA根证书: C:\读书郎教育科技有限公司\downstream_root.cer
```

证书加载失败时的日志：
```
警告：加载客户端证书失败: open C:\读书郎教育科技有限公司\cert.pem: no such file or directory
将使用不带证书的连接
```

## 常见问题

### 1. 证书文件不存在

**现象**: 显示"加载客户端证书失败"警告
**解决**: 确认证书文件已正确放置在 `C:\读书郎教育科技有限公司\` 目录

### 2. 权限问题

**现象**: 显示"读取CA证书失败"错误
**解决**: 确认应用程序有读取证书目录的权限

### 3. 证书格式错误

**现象**: 显示"解析CA证书失败"警告
**解决**: 确认证书文件格式正确（PEM格式）

## 安全建议

1. **目录权限**: 限制证书目录的访问权限
2. **文件权限**: 设置私钥文件为只读
3. **定期更新**: 监控证书过期时间，及时更新
4. **备份**: 安全备份证书文件

## 技术实现

### 核心代码逻辑

```go
// 固定证书路径
certDir := "C:\\读书郎教育科技有限公司"
certFile := certDir + "\\cert.pem"
keyFile := certDir + "\\prikey.txt"
caFile := certDir + "\\downstream_root.cer"

// 容错加载证书
cert, err := tls.LoadX509KeyPair(certFile, keyFile)
if err != nil {
    // 显示警告但继续执行
    fmt.Printf("警告：加载客户端证书失败: %v\n", err)
} else {
    // 成功加载证书
    tlsConfig.Certificates = []tls.Certificate{cert}
}
```

这种设计确保了即使证书文件缺失，系统仍能正常运行，只是会使用不带证书的连接方式。
