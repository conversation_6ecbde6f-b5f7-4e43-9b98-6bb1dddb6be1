package v1

import (
	"account/global"
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"
	"math/rand"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// EbagRegister 电子书包注册
// login no
// @Param sn string true "签名"
// @Param ebagid string true "电子书包id"
// @Success 200 {json} json {"uid": 1, "username": "", "forbidden": 0, "access_token": "", "access_expire": **********}
// @Router /ebag/register [get]
func EbagRegister(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	ebagid := GetRequestParam(c, "ebagid")
	if len(ebagid) != 36 {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	m, err := service.GetMemberByUsername(ebagid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			password := GetRequestParam(c, "password")
			if password == "" {
				password = strconv.Itoa(rand.Intn(********))
			}
			now := time.Now().Unix()
			timestr := strconv.FormatInt(now, 10)
			m.Username = ebagid
			service.FormatMemberPassword(m, password)
			m.Forbidden = 0
			m.AccessToken = utils.MD5(ebagid + timestr)
			m.AccessExpire = now + global.TOKEN_EXPIRE
			m.Regip = GetIp(c)
			m.Regdate = now
			m.Regapp = GetAppid(c)
			err = service.CreateMember(m)
		}
		if err != nil {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
	}
	c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
	response.OkWithData(getMemberLoginInfo(m), c)
}

// EbagCheck 电子书包id校验
// login no
// @Param sn string true "签名"
// @Param ebagid string true "电子书包id"
// @Success 200 {json} json {"uid": 1, "username": "", "forbidden": 0, "access_token": "", "access_expire": **********}
// @Router /ebag/check [get]
func EbagCheck(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	ebagid := GetRequestParam(c, "ebagid")
	if len(ebagid) != 36 {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	m, err := service.GetMemberByUsername(ebagid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	m.AccessExpire = time.Now().Unix() + global.TOKEN_EXPIRE
	if err = service.SaveMemberAccess(m); err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	} else {
		c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
		data := getMemberLoginInfo(m)
		data["access_expire"] = m.AccessExpire
		response.OkWithData(data, c)
	}
}
