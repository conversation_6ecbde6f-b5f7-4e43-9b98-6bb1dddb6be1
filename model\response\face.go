package response

import (
	"time"
)

type FaceLoginRes struct {
	AccessExpire int64  `json:"access_expire"`
	AccessToken  string `json:"access_token"`
	Avatar       string `json:"avatar"`
	Mobile       string `json:"mobile"`
	Uid          uint   `json:"uid"`
	Username     string `json:"username"`
}

type (
	NativeDeviceFaceRes struct {
		List []NativeUserFace `json:"list"`
	}

	NativeUserFace struct {
		ID        uint64 `json:"id"`
		Serial    string `json:"serial"` // 序列号
		Uid       uint   `json:"uid"`
		ImageUrl  string `json:"image_url"` // 图片url
		FaceUrl   string `json:"face_url"`  // 人脸特征
		CreatedAt int64  `json:"created_at"`
	}
)

type (
	NativeUserFaceRes struct {
		Total int64                `json:"total" doc:"total"`
		List  []NativeUserFaceItem `json:"list" doc:"list"`
	}
	NativeUserFaceItem struct {
		ID         int       `json:"id" doc:"id"`
		Uid        int       `json:"uid" doc:"uid"`
		Serial     string    `json:"serial" doc:"serial"`
		Model      string    `json:"model" doc:"model"`
		ImageUrl   string    `json:"image_url" doc:"image_url"`
		CreatedAt  time.Time `json:"-" doc:"-"`
		CreatedAtJ int64     `gorm:"-" json:"created_at" doc:"created_at"`
		Mobile     string    `json:"mobile" doc:"mobile"`
		Nickname   string    `json:"nickname" doc:"nickname"`
	}
)
