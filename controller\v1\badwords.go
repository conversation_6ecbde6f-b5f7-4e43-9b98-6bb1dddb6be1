package v1

import (
	"account/global"
	"account/model/response"
	"account/service"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// BadwordsCheck 关键字检查
// @Param key string true "关键字"
// @Success {json} json {"isbadword": "Yes"}
// @Router /badwords/check
func BadwordsCheck(c *gin.Context) {
	key := GetRequestParam(c, "key")
	isBadwords := "No"
	if key != "" {
		pass, err := service.ShumeiTextCheck(key)
		if err == nil {
			if !pass {
				isBadwords = "Yes"
			}
		} else {
			if ok, _ := global.Filter.Validate(strings.ToLower(key)); !ok {
				isBadwords = "Yes"
			}
		}
	}
	response.OkWithData(gin.H{"isBadwords": isBadwords}, c)
}

// BadWordsCheckstr 相似关键字检查
// @Param key string true "关键字"
// @Success {json} json {"isbadword": "Yes"}
// @Router /badwords/checkstr
func BadWordsCheckstr(c *gin.Context) {
	key := GetRequestParam(c, "key")
	isBadwords := "No"
	var data string
	if key != "" {
		ok, word := global.Filter.Validate(strings.ToLower(key))
		if !ok {
			isBadwords = "Yes"
			data = word
		}
	}
	response.OkWithData(gin.H{"isBadwords": isBadwords, "word": data}, c)
}

// BadWordsAdd 增加关键字
// @Param key string true "关键字，可以为多个关键字，用 , 分割"
// @Success {json} json {"success": true}
// @Router /badwords/add
func BadwordAdd(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	key := GetRequestParam(c, "key")
	words := strings.Split(key, ",")
	var success bool
	if len(words) > 0 {
		global.Filter.AddWord(words...)
		if err := service.AddBadword(words); err != nil {
			global.LOG.Error("add badword", zap.Error(err), zap.String("data", key))
		}
		success = true
	}
	response.OkWithData(success, c)
}

// BadwordDel 删除关键字
// @Param key string true "关键字，可以为多个关键字，用 , 分割"
// @Success {json} json {"success": true}
// @Router /badwords/del
func BadwordDel(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	key := GetRequestParam(c, "key")
	words := strings.Split(key, ",")
	var success bool
	if len(words) > 0 {
		global.Filter.DelWord(words...)
		if err := service.DelBadword(words); err != nil {
			global.LOG.Error("del badword", zap.Error(err), zap.String("data", key))
		}
		success = true
	}
	response.OkWithData(success, c)
}
