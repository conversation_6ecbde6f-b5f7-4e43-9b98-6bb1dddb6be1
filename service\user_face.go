package service

import (
	"account/global"
	"account/model"
	"account/model/request"
	"account/model/response"
	"time"

	"gorm.io/gorm"
)

// 查询机器人脸数据
func GetDeviceUserFace(serial string, limit int) (data []model.UserFace, err error) {
	err = global.DB.Model(&data).Where("serial = ?", serial).
		Limit(limit).
		Find(&data).Error
	return
}

// 查询用户人脸数据
func GetUserFaceByUidSerial(uid int, serial string) (data model.UserFace, err error) {
	err = global.DB.Model(&data).Where("serial = ? And uid = ?", serial, uid).Last(&data).Error
	return
}

// 绑定人脸数据（一对一）
func FaceBind(face *model.UserFace) (err error) {
	db := global.DB.Model(&face).Exec("Insert into user_face(`uid`,`serial`,`model`,`face_id`,`image_url`,`face_url`,`created_at`) Select ?, ?, ?, ?, ?, ?, ? From dual Where Not Exists (Select id From user_face Where serial = ? And deleted_at is null)",
		face.Uid, face.Serial, face.Model, face.FaceId, face.ImageUrl, face.FaceUrl, time.Now(),
		face.Serial,
	)
	err = db.Error
	if err != nil {
		err = response.New(response.E_DATABASE, "", err)
		return
	}
	if db.RowsAffected == 0 {
		err = response.New(response.E_MEMBER_ALREADY_EXISTED, "已添加人脸数据，不可重复添加", nil)
		return
	}
	faces, err := GetDeviceUserFace(face.Serial, 1)
	if err != nil {
		err = response.New(response.E_DATABASE, "", err)
		return
	}
	if len(faces) == 0 {
		err = response.New(response.E_UNKNOWN, "添加人脸数据失败", nil)
		return
	}
	face.ID = faces[0].ID
	return
}

// 通过id删除人脸数据
func DeleteUserFaceById(ids []int64) (err error) {
	if len(ids) == 0 {
		return
	}
	err = global.DB.Model(&model.UserFace{}).Where("id in (?)", ids).Delete(&map[string]interface{}{}).Error
	if err != nil {
		err = response.New(response.E_DATABASE, "", err)
		return
	}

	return
}

// 获取机器一对一人脸数据
func GetNativeDevicesFace(serials []string) (data response.NativeDeviceFaceRes, err error) {
	var list []model.UserFace
	err = global.DB.Model(&model.UserFace{}).Where("serial in (?)", serials).
		Group("serial").Find(&list).Error
	if err != nil {
		err = response.New(response.E_DATABASE, "", err)
		return
	}
	m := make(map[string]model.UserFace)
	for i := range list {
		m[list[i].Serial] = list[i]
	}
	for _, v := range serials {
		data.List = append(data.List, response.NativeUserFace{
			ID:       m[v].ID,
			Serial:   v,
			Uid:      m[v].Uid,
			ImageUrl: m[v].ImageUrl,
			FaceUrl:  m[v].FaceUrl,
			CreatedAt: func() int64 {
				if m[v].CreatedAt.IsZero() {
					return 0
				}
				return m[v].CreatedAt.Unix()
			}(),
		})
	}
	return
}

// 获取人脸管理数据
func GetNativeUserFace(req *request.NativeUserFaceReq) (data response.NativeUserFaceRes, err error) {
	sub := global.DB.Table("user_face u").Joins("Left Join ac_submembers s on s.suid = u.uid").
		Where("u.deleted_at is null").
		Select([]string{"u.id", "u.uid", "u.serial", "u.model", "u.image_url", "u.created_at",
			"IFNULL(s.uid, u.uid) puid"})
	if req.Uid > 0 {
		sub = sub.Where("u.uid = ?", req.Uid)
	}
	if req.Serial != "" {
		sub = sub.Where("u.serial = ?", req.Serial)
	}
	if req.Model != "" {
		sub = sub.Where("u.model = ?", req.Model)
	}

	db := global.DB.Table("(?) uf", sub).
		Joins("Join user_profile p on uf.uid = p.uid").
		Joins("Join ac_members m on uf.puid = m.uid")
	if req.Mobile != "" {
		db = db.Where("m.mobile = ?", req.Mobile)
	}
	if req.Nickname != "" {
		db = db.Where("p.realname like ?", "%"+req.Nickname+"%")
	}

	err = db.Session(&gorm.Session{}).Count(&data.Total).Error
	if err != nil {
		err = response.New(response.E_DATABASE, "", err)
		return
	}

	if req.PageSize > 0 {
		db = db.Limit(req.PageSize)
	}
	if req.Page > 0 {
		db = db.Offset((req.Page - 1) * req.PageSize)
	}
	err = db.Order("uf.id DESC").Select([]string{"uf.*", "m.mobile", "p.realname nickname"}).
		Find(&data.List).Error
	if err != nil {
		err = response.New(response.E_DATABASE, "", err)
		return
	}
	for i := range data.List {
		data.List[i].CreatedAtJ = data.List[i].CreatedAt.Unix()
	}
	return
}
