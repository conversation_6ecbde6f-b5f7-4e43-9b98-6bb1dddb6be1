package v1

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"account/global"
	"account/model"
	"account/model/request"
	"account/model/response"
	"account/service"
	"account/utils"
	"account/utils/ebag"
	"account/utils/ws"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var upgrader = websocket.Upgrader{}

func WsScanLogin(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	serial := GetDeviceSerial(c)
	if serial == "" {
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	conn, err := upgrader.Upgrade(c.<PERSON>, c.Request, nil)
	if err != nil {
		c.AbortWithError(http.StatusInternalServerError, err)
		global.LOG.Info("upgrade", zap.Strings("errors", c.Errors.Errors()))
		return
	}
	client := &ws.Client{
		ID:     serial,
		Socket: conn,
		Send:   make(chan interface{}, 1),
	}
	ws.ScanLoginManager.Register(client)

	go client.NothingRead()

	go func() {
		defer func() {
			conn.WriteMessage(websocket.CloseMessage, []byte{})
			conn.Close()
		}()
		for {
			select {
			// TODO 确定心跳间隔，超出时间主动关闭链接
			case msg, ok := <-client.Send:
				if !ok {
					return
				}
				if bmsg, ok := msg.([]byte); ok {
					conn.WriteMessage(websocket.TextMessage, bmsg)
				} else {
					conn.WriteJSON(msg)
				}
			}
		}
	}()
}

// 上报扫描同意
func ScanFeedback(c *gin.Context) {
	feedback := GetRequestParam(c, "feedback")
	origin := GetRequestParam(c, "origin")
	serial := GetRequestParam(c, "serial")
	ticket := GetRequestParam(c, "ticket")

	if strings.ToLower(origin) != "pad" || serial == "" {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Info("recover", zap.Any("msg", r), zap.String("stack", string(debug.Stack())))
		}
		if len(c.Errors) > 0 {
			global.LOG.Info(c.Request.RequestURI, zap.Strings("errors", c.Errors.Errors()))
		}
	}()
	var agree bool
	if feedback == "agree" {
		agree = true
	}
	if !agree || ticket == "" {
		msg := response.ScanLoginResp{
			Errno:    response.E_NOT_LOGIN,
			Errmsg:   "获取登录凭证失败",
			Feedback: feedback,
		}
		ws.ScanLoginManager.Send(serial, msg)
		response.OkWithData(gin.H{"status": 1}, c)
		return
	}
	zjUsers, err := ebag.GetZhujiUserInfo(ticket)
	if err != nil {
		c.Error(err)
		msg := response.ScanLoginResp{
			Errno:  response.E_EBAG_USERINFO,
			Errmsg: "获取用户信息失败",
		}
		if !ws.ScanLoginManager.Send(serial, msg) {
			response.FailWithErrcode(response.E_PAD_DISCONN, "平板链接已断开", c)
			return
		}
		response.FailWithErrcode(response.E_EBAG_USERINFO, "获取用户信息失败", c)
		return
	}

	users := request.NewMembersFormZhujiUser(append([]ebag.ZhujiUserInfo{zjUsers.UserInfo}, zjUsers.ChildList...))
	_, children, newIds, err := getOrCreateMembers(c, users)
	if err != nil {
		c.Error(err)
		msg := response.ScanLoginResp{
			Errno:  response.E_UNKNOWN,
			Errmsg: "未知错误",
		}
		if !ws.ScanLoginManager.Send(serial, msg) {
			response.FailWithErrcode(response.E_PAD_DISCONN, "平板链接已断开", c)
			return
		}
		response.FailWithErrcode(response.E_UNKNOWN, "", c)
		return
	}
	msg := response.ScanLoginResp{
		Platform: children,
		NewUid:   newIds,
	}
	if !ws.ScanLoginManager.Send(serial, msg) {
		response.FailWithErrcode(response.E_PAD_DISCONN, "平板链接已断开", c)
		return
	}
	response.OkWithData(gin.H{"status": 1}, c)
}

func getOrCreateMembers(c *gin.Context, user []request.UserInfo) (pid uint, children []map[string]interface{}, newUids []uint, err error) {
	tx := global.DB.Begin()
	ctx := global.InjectDB(c, tx)
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()
	ip := GetIp(c)
	regapp := GetAppid(c)
	platform := GetRequestParam(c, "platform")
	if platform == "" {
		platform = "330681"
	}
	var (
		mobile string
	)
	for i, u := range user {
		if u.CardNo == "" {
			continue
		}
		var (
			uc    *model.UserProfileExtra //
			m     *model.AcMember         // 账号
			pf    *model.UserProfile      // 个人信息
			isreg bool
		)
		uname := u.ToAcMember("", "").Username
		m, err = service.GetMemberByAccount(uname)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if m == nil || m.Uid == 0 {
			m = u.ToAcMember(ip, regapp)
			if uc != nil {
				m.Uid = uc.Uid
			}
			if err = service.CreateMemberCtx(ctx, m); err != nil {
				return
			}
			isreg = true
			newUids = append(newUids, m.Uid)
			uc = u.ToUserProfileExtra(m.Uid)
			if err = service.CreateUserProfileExtra(ctx, uc); err != nil {
				return
			}
		}
		if i == 0 {
			pid = m.Uid
			mobile = u.Mobile
		}
		pf, err = service.GetProfileByUid(m.Uid)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if pf == nil || pf.Uid == 0 {
			pf = u.ToUserProfile(int(m.Uid))
			if !isreg {
				pf.Username = m.Username
			}
			if err = service.CreateProfileCtx(ctx, pf); err != nil {
				return
			}
		}
		// 生成子账号关联
		if i > 0 {
			var exists bool
			exists, err = service.ExistsSubmember(c, pid, m.Uid)
			if err != nil {
				return
			}
			if !exists {
				if err = service.CreatePlatformSubmember(ctx, &model.PlatformSubmember{
					Suid:     m.Uid,
					Uid:      pid,
					Platform: platform,
					Sort:     i,
				}); err != nil {
					return
				}
			}
		}
		// 更新数据（token过期时间、附加信息、用户个人信息）
		if !isreg {
			m.AccessExpire = time.Now().Unix() + global.TOKEN_EXPIRE
			if err = service.SaveMemberAccessCtx(ctx, m); err != nil {
				return
			}
			if err = service.UpdateUserProfileExtra(ctx, u.ToUserProfileExtra(m.Uid)); err != nil {
				return
			}
			p := u.ToUserProfile(int(m.Uid))
			if err = service.UpdateProfile(ctx, &model.UserProfile{
				Uid:      p.Uid,
				Realname: p.Realname,
				BirthY:   p.BirthY,
				BirthM:   p.BirthM,
				BirthD:   p.BirthD,
				School:   p.School,
			}); err != nil {
				return
			}
		}
		if i > 0 {
			var cd map[string]interface{}
			json.Unmarshal(u.RawData, &cd)
			cd["uid"] = pid
			cd["suid"] = m.Uid
			cd["username"] = m.Username
			cd["mobile"] = mobile
			cd["access_token"] = m.AccessToken
			cd["access_expire"] = m.AccessExpire
			cd["avatar"] = utils.GetAvatarUri(m.Uid)
			cd["realname"] = pf.Realname
			cd["gender"] = pf.Gender
			cd["birthday"] = fmt.Sprintf("%d-%02d-%02d", pf.BirthY, pf.BirthM, pf.BirthD)
			children = append(children, cd)
		}
	}
	return
}

func getSubmemList(puid uint, mobile string) (data []response.SubMemResp, err error) {
	slist, err := service.GetSubmembersByUid(puid)
	if err != nil {
		return
	}
	pm, err := service.GetMemberByUid(strconv.Itoa(int(puid)))
	if err != nil {
		return
	}
	if len(slist) == 0 {
		m := model.AcSubmember{
			ID:   0,
			Suid: puid,
			Uid:  puid,
			Sort: 0,
		}
		err = service.CreateSubmember(&m)
		if err != nil {
			return
		}
		slist = append(slist, m)
	}
	suids := make([]uint, 0, len(slist))
	for i := range slist {
		suids = append(suids, slist[i].Suid)
	}
	pMap, err := service.GetProfileMapByUids(suids, "uid,username,realname,birth_y,birth_m,birth_d,gender,grade,school")
	if err != nil {
		return
	}
	data = make([]response.SubMemResp, 0, len(suids))
	for _, uid := range suids {
		if p, ok := pMap[uid]; ok {
			subm := response.SubMemResp{
				Uid:      puid,
				Suid:     uid,
				Username: p.Username,
				Mobile:   mobile,
				Avatar:   utils.GetAvatarUri(uid),
				Realname: p.Realname,
				Gender:   p.Gender,
				Grade:    p.Grade,
				School:   p.School,
			}
			if subm.Suid == pm.Uid {
				subm.AccessToken = pm.AccessToken
				subm.AccessExpire = pm.AccessExpire
				if pm.Mobile != "" {
					subm.Mobile = pm.Mobile
				}
			}
			if p.BirthY > 0 && p.BirthM > 0 && p.BirthD > 0 {
				subm.Birthday = fmt.Sprintf("%d-%02d-%02d", p.BirthY, p.BirthM, p.BirthD)
			}
			data = append(data, subm)
		}
	}
	return
}
