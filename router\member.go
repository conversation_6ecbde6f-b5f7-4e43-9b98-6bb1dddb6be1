package router

import (
	v1 "account/controller/v1"

	"github.com/gin-gonic/gin"
)

func InitMemberRouter(Router *gin.RouterGroup) {
	ApiRouter := Router.Group("member")
	{
		Any(ApiRouter, "count", v1.MemberCount)
		Any(ApiRouter, "login", v1.MemberLogin)
		Any(ApiRouter, "status", v1.MemberStatus)
		Any(ApiRouter, "chpwd", v1.MemberChpwd)
		Any(ApiRouter, "third/register", v1.MemberThirdRegister)
		Any(ApiRouter, "status2", v1.MemberStatus2)
		Any(ApiRouter, "login2", v1.MemberLogin2)

		ApiRouter.GET("scan/login", v1.WsScanLogin)
		ApiRouter.POST("scan/feedback", v1.ScanFeedback)

		ApiRouter.GET("device", v1.DeviceMember)
		ApiRouter.POST("unbindDevice", v1.UnbindDevice)
	}
}
