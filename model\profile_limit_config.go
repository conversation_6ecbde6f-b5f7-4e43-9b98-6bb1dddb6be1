package model

type ProfileLimitConfig struct {
	ID     int
	Type   int
	Status int    // 状态。0-禁用，1-启用
	Ip     string // * 或空表示所有。***********/24 或 *************，多个使用英文逗号分隔
}

const (
	ProfileLimitConfigType         = iota
	ProfileLimitConfigTypeAvatar   // 头像
	ProfileLimitConfigTypeNickname // 昵称
	ProfileLimitConfigTypeSchool   // 学校
)

func (ProfileLimitConfig) TableName() string {
	return "profile_limit_config"
}
