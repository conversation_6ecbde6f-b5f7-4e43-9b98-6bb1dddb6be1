package main

import (
	"account/core"
	"account/global"
	"account/initialize"
	"account/utils/ws"
	"math/rand"
	"time"
)

func init() {
	rand.Seed(time.Now().Unix())   // 初始化随机数种子
	global.VIPER = core.Viper()    // 初始化Viper
	core.Apis(global.VIPER)        // 初始化接口调用
	global.LOG = core.Zap()        // 初始化zap日志库
	global.DB = initialize.Mysql() // gorm连接数据库
	global.Redis = initialize.Redis()

	global.Filter = initialize.InitFilter() // 敏感词过滤器初始化
	go ws.ScanLoginManager.Start()
}

func main() {
	// 程序结束前关闭数据库链接
	if global.DB != nil {
		db, _ := global.DB.DB()
		defer db.Close()
	}
	if global.Redis != nil {
		defer global.Redis.Close()
	}

	core.RunWindowsServer()
}
