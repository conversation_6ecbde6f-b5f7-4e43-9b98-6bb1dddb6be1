package v1

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/service"
	"account/utils"
	"encoding/hex"
	"path"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// FaceDevice 获取机器录入的人脸数据
// login no
// @Param sn string true "签名。checkstr=serial"
// @Success 200 {json} json {"data": [{"uid":0,"face_id":"","image_url":"","face_url":""}]}
// @Router /face/device [get]
func FaceDevice(c *gin.Context) {
	serial := GetDeviceSerial(c)
	if serial == "" {
		response.FailWithErrcode(response.E_PARAM, "缺少设备信息参数", c)
		return
	}
	reason, desp, _ := checkSignature2(c, global.SN_EXPIRE, serial, nil)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	// TODO 获取设备保存的人脸库
	faces, err := service.GetDeviceUserFace(serial, 20)
	if err != nil {
		c.Error(err)
		response.FailWithErrcode(response.E_UNKNOWN, "", c)
		return
	}
	// TODO 考虑人脸库数据是否加密传输
	response.OkWithData(gin.H{
		"status": 1,
		"data":   faces,
	}, c)
}

// FaceBind 机器绑定人脸数据（一台机器只能绑定一个人脸信息）
// login yes
// @Param sn string true "签名"
// @Param face_blob string true "人脸特征数据，字节数组的base64编码" ""
// @Param image form-file true "人脸图片文件"
// @Success 200 {json} json {"data": {"uid":0,"face_id":"","image_url":"","face_url":""}}
// @Router /face/bind [post]
func FaceBind(c *gin.Context) {
	m, ok := CheckLogin(c)
	if !ok {
		return
	}

	// 获取设备信息
	serial := GetDeviceSerial(c)
	if serial == "" {
		response.FailWithErrcode(response.E_PARAM, "缺少设备信息参数", c)
		return
	}
	dmodel := GetDeviceModel(c)
	if dmodel == "" {
		response.FailWithErrcode(response.E_PARAM, "缺少设备信息参数", c)
		return
	}
	dir := "apps/ac_center/face"
	datepath := time.Now().Format("200601")
	// 获取人脸特征文件
	blobUrl := path.Join(dir, datepath, utils.Uniqid())
	if err := uploadFile(c, "face_blob", blobUrl); err != nil {
		response.FailWithError(err, c)
		return
	}
	imgUrl := path.Join(dir, datepath, utils.Uniqid())
	if err := uploadFile(c, "image", imgUrl); err != nil {
		response.FailWithError(err, c)
		return
	}

	// 绑定人脸数据
	face := model.UserFace{
		Uid:      m.Uid,
		Serial:   serial,
		Model:    dmodel,
		FaceId:   hex.EncodeToString(uuid.NewV4().Bytes()),
		ImageUrl: imgUrl,
		FaceUrl:  blobUrl,
	}
	if err := service.FaceBind(&face); err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{
		"data": face,
	}, c)
}

func uploadFile(c *gin.Context, formFilename, dst string) (err error) {
	file, err := c.FormFile(formFilename)
	if err != nil {
		err = response.New(response.E_PARAM, "缺少文件数据", err)
		return
	}
	if file.Size == 0 {
		err = response.New(response.E_PARAM, "文件大小错误", err)
		return
	}
	fd, err := file.Open()
	if err != nil {
		err = response.New(response.E_UNKNOWN, "临时文件打开失败", err)
		return
	}
	defer fd.Close()
	if err = utils.UploadFile(fd, dst); err != nil {
		err = response.New(response.E_UNKNOWN, "上传失败", err)
		return
	}
	return nil
}

// FaceLogin 人脸登录
// login no
// @Param sn string true "签名。uid>0；checkstr=faceId"
// @Param nonce string true
// @Success 200 {json} json {"data": {"uid":0,"image_url":"","face_blob":""}}
// @Router /face/login [post]
func FaceLogin(c *gin.Context) {
	serial := GetDeviceSerial(c)
	if serial == "" {
		response.FailWithErrcode(response.E_PARAM, "缺少设备信息参数", c)
		return
	}
	reason, desp := ParseSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	faceId := ""
	uid := c.GetInt("uid")
	if uid > 0 {
		face, _ := service.GetUserFaceByUidSerial(uid, serial)
		if face.ID > 0 {
			faceId = face.FaceId
		}
	}
	if faceId == "" {
		response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "未录入人脸数据", c)
		return
	}
	nonce, ok := CheckNonce(c, serial, 300)
	if !ok {
		return
	}
	reason, _, _ = checkSignature2(c, 300, faceId+nonce, nil)
	if reason < 0 {
		response.FailWithErrcode(response.E_ACCOUNT_MISMATCH, "人脸数据不匹配", c)
		return
	}
	m, _ := service.GetMemberByUid(strconv.Itoa(uid))
	if m == nil || m.Uid == 0 {
		response.FailWithErrcode(response.E_FORBIDDEN, "", c)
		return
	}
	if !CheckDeviceLoginMember(c, m) {
		return
	}
	now := time.Now().Unix()
	if now >= m.AccessExpire {
		m.AccessToken = service.CreateMemberAccessToken(m.Uid, now)
	}
	m.AccessExpire = now + global.TOKEN_EXPIRE
	if err := service.SaveMemberAccess(m); err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
	response.OkWithData(gin.H{
		"data": response.FaceLoginRes{
			AccessExpire: m.AccessExpire,
			AccessToken:  m.AccessToken,
			Avatar:       utils.GetAvatarUri(m.Uid),
			Mobile:       m.Mobile,
			Uid:          m.Uid,
			Username:     m.Username,
		},
	}, c)
}
