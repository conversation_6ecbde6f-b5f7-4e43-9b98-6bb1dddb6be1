package response

type PlatformMemberResp struct {
	Uid          uint   `json:"uid"`
	Suid         uint   `json:"suid"`
	Username     string `json:"username"`
	Mobile       string `json:"mobile"`
	Avatar       string `json:"avatar"`
	AccessToken  string `json:"access_token,omitempty"`
	AccessExpire int64  `json:"access_expire,omitempty"`
	Realname     string `json:"realname"`
	Gender       uint   `json:"gender"`
	Birthday     string `json:"birthday"`
	Grade        int    `json:"grade"`
	School       string `json:"school"`
	Class        string `json:"class"` // 班级
}

type ScanLoginResp struct {
	Errno    int         `json:"errno"`
	Errmsg   string      `json:"errmsg"`
	Feedback string      `json:"feedback,omitempty"`
	Platform interface{} `json:"platform,omitempty"`
	Readboy  interface{} `json:"readboy,omitempty"`
	NewUid   []uint      `json:"new_uid,omitempty"`
}

type SubMemResp struct {
	Uid          uint   `json:"uid"`
	Suid         uint   `json:"suid"`
	Username     string `json:"username"`
	Mobile       string `json:"mobile"`
	Avatar       string `json:"avatar"`
	AccessToken  string `json:"access_token,omitempty"`
	AccessExpire int64  `json:"access_expire,omitempty"`
	Realname     string `json:"realname"`
	Gender       uint   `json:"gender"`
	Birthday     string `json:"birthday"`
	Grade        int    `json:"grade"`
	School       string `json:"school"`
}
