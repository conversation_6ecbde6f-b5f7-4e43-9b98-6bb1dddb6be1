package initialize

import (
	"account/global"

	"github.com/go-redis/redis"
	"go.uber.org/zap"
)

func Redis() *redis.Client {
	uri := global.VIPER.GetString("redis.uri")
	options, err := redis.ParseURL(uri)
	if err != nil {
		global.LOG.Error("", zap.Error(err))
		return nil
	}
	client := redis.NewClient(options)
	_, err = client.Ping().Result()
	if err != nil {
		global.LOG.Error("", zap.Error(err))
		return nil
	}
	return client
}
