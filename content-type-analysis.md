# Content-Type 问题分析

## 问题现象

国标库返回错误：
```json
{
  "resultCode": "M000002",
  "resultDesc": "Content-Type error"
}
```

## 文档 vs 实际

### 国标文档要求
- 协议：HTTP
- 数据格式：JSON 字符串
- Content-Type：`application/json`
- 编码：UTF-8

### 实际测试发现
根据您提供的 Go 示例代码：
```go
resp, err := client.Post("https://1.30.133.75:20002/envelopData",
    "application/octet-stream", strings.NewReader("123"))
```

实际使用的是 `application/octet-stream`！

## 解决方案

### 当前实现
```go
// 使用 application/octet-stream（根据实际测试）
req.Header.Set("Content-Type", "application/octet-stream")
```

### 可能的原因

1. **接口版本差异**：
   - 文档描述的是标准接口
   - 实际部署的可能是特定版本

2. **数据封装方式**：
   - JSON 数据可能需要特殊编码
   - 可能需要二进制传输

3. **网关或代理**：
   - 中间可能有网关转换
   - 代理服务器可能有特殊要求

## 测试结果对比

### 使用 application/json
```
发送请求头: Content-Type=application/json
国标库响应: {"resultCode":"M000002","resultDesc":"Content-Type error"}
```

### 使用 application/octet-stream
```
发送请求头: Content-Type=application/octet-stream
国标库响应: [待测试]
```

## 建议的测试步骤

1. **测试 octet-stream**：
   ```bash
   # 重新启动应用，查看日志
   ```

2. **如果仍有问题，尝试其他 Content-Type**：
   - `text/plain`
   - `application/x-www-form-urlencoded`
   - `multipart/form-data`

3. **检查请求体格式**：
   - 确认 JSON 格式正确
   - 检查是否需要特殊编码

## 调试信息

当前代码会输出：
```
发送请求头: Content-Type=application/octet-stream
请求体长度: XXX 字节
请求体前100字符: {"bizPackage":{"appName":"...
```

## 其他可能的解决方案

### 1. 尝试不同的数据格式
```go
// 可能需要直接发送 JSON 字符串，而不是嵌套在 bizPackage 中
jsonData, err := json.Marshal(req) // 直接序列化请求对象
```

### 2. 尝试不同的编码
```go
// 可能需要 Base64 编码
encodedData := base64.StdEncoding.EncodeToString(jsonData)
```

### 3. 检查 URL 路径
确认使用的是正确的接口路径：
- 测试环境：`/envelopData`
- 生产环境：`/uentrance/interf/auth/request`

## 下一步行动

1. 测试当前的 `application/octet-stream` 修改
2. 如果仍有问题，尝试直接发送原始 JSON（不嵌套在 bizPackage 中）
3. 检查是否需要特殊的认证头或签名

## 参考

- 您提供的 Go 示例使用 `application/octet-stream`
- 国标文档要求 `application/json`
- 实际测试表明需要 `application/octet-stream`

这种文档与实际不符的情况在集成第三方 API 时比较常见，需要以实际测试结果为准。
