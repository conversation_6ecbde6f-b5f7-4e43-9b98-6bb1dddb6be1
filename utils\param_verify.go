package utils

import (
	"regexp"
	"strconv"
)

var (
	r_username   = regexp.MustCompile(`^[a-zA-Z0-9_.@]{6,32}$`)
	r_email      = regexp.MustCompile(`(?i)^[a-zA-Z0-9_.-]{2,32}@([a-zA-Z0-9.]+)$`)
	r_phone      = regexp.MustCompile(`^1[3456789][0-9]{9}$`)
	r_password   = regexp.MustCompile(`^[a-fA-F0-9]{32}$`)
	r_uids       = regexp.MustCompile(`^([\d]+,)*([\d]+)$`)
	r_wx_unionid = regexp.MustCompile(`^[a-zA-Z0-9_-]{28,29}$`)
)

func TestUsername(username string) bool {
	return r_username.Match([]byte(username))
}

func TestEmail(email string) bool {
	return r_email.Match([]byte(email))
}

func TestPhone(phone string) bool {
	_, err := strconv.Atoi(phone)
	return err == nil
	// return r_phone.Match([]byte(phone))
}

func TestPassword(password string) bool {
	return r_password.Match([]byte(password))
}

func TestUids(uids string) bool {
	return r_uids.Match([]byte(uids))
}

func TestWxUnionid(unionid string) bool {
	return r_wx_unionid.Match([]byte(unionid))
}
