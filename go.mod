module account

go 1.14

require (
	github.com/aliyun/aliyun-oss-go-sdk v2.1.6+incompatible
	github.com/aliyunmq/mq-http-go-sdk v1.0.3
	github.com/baiyubin/aliyun-sts-go-sdk v0.0.0-**************-cfa1a18b161f // indirect
	github.com/facebookgo/stack v0.0.0-**************-************ // indirect
	github.com/fsnotify/fsnotify v1.4.9
	github.com/fvbock/endless v0.0.0-**************-447134032cb6
	github.com/gin-gonic/gin v1.6.3
	github.com/go-gomail/gomail v0.0.0-**************-81ebce5c23df
	github.com/go-playground/validator/v10 v10.2.0
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-resty/resty/v2 v2.11.0
	github.com/go-sql-driver/mysql v1.8.1 // indirect
	github.com/gogap/errors v0.0.0-**************-531a6449b28c // indirect
	github.com/gogap/stack v0.0.0-**************-fef68dddd4f8 // indirect
	github.com/gorilla/websocket v1.5.0
	github.com/kr/pretty v0.3.1 // indirect
	github.com/lestrrat-go/file-rotatelogs v2.4.0+incompatible
	github.com/lestrrat-go/strftime v1.0.4 // indirect
	github.com/onsi/gomega v1.16.0 // indirect
	github.com/satori/go.uuid v1.2.0
	github.com/spf13/viper v1.7.1
	github.com/valyala/fasthttp v1.22.0 // indirect
	go.uber.org/zap v1.10.0
	golang.org/x/net v0.27.0 // indirect
	golang.org/x/text v0.20.0 // indirect
	golang.org/x/time v0.6.0 // indirect
	gopkg.in/alexcesaro/quotedprintable.v3 v3.0.0-20150716171945-2caba252f4dc // indirect
	gopkg.in/gomail.v2 v2.0.0-**************-81ebce5c23df // indirect
	gorm.io/driver/mysql v1.5.7
	gorm.io/gorm v1.25.12
)
