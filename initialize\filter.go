package initialize

import (
	"account/global"
	"account/model"
	"account/pkg/sensitive"
)

func InitFilter() *sensitive.Filter {
	f := sensitive.New()
	f.<PERSON>(global.VIPER.GetString("filter.dict"))
	f.UpdateNoise<PERSON>attern(global.VIPER.GetString("filter.noise"))
	go func() {
		var id int
		db := global.DB.Table("badwords")
		limit := 3000
		for {
			var words []model.Badword
			db.Where("id > ?", id).Order("id").Limit(limit).Find(&words)
			for i := range words {
				f.Add<PERSON>ord(words[i].Key)
			}
			id = words[len(words)-1].ID
			if len(words) < limit {
				break
			}
		}
	}()
	return f
}
