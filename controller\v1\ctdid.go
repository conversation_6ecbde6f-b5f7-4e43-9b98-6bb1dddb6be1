package v1

import (
	"account/global"
	"account/model/request"
	"account/model/response"
	"account/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CtdidAuth 网络身份认证接口
// @Summary 网络身份认证
// @Description 提供网络身份认证凭证等七种认证业务模式
// @Tags 网络身份认证
// @Accept json
// @Produce json
// @Param request body request.CtdidAuthRequest true "认证请求数据"
// @Success 200 {object} gin.H "认证响应"
// @Router /ctdid/auth [post]
func CtdidAuth(c *gin.Context) {
	var req request.CtdidAuthBizPackage

	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithErrcode(response.E_PARAM, "请求参数格式错误"+err.Error(), c)
		return
	}

	// 验证Content-Type
	contentType := c.GetHeader("Content-Type")
	if contentType != "application/json" {
		response.FailWithErrcode(response.E_PARAM, "Content-Type必须为application/json", c)
		return
	}

	// 验证请求体大小（小于100KB）
	if c.Request.ContentLength > 100*1024 {
		response.FailWithErrcode(response.E_PARAM, "请求包大小超出限制", c)
		return
	}

	// 验证认证模式
	if !req.ValidateMode() {
		response.FailWithErrcode(response.E_PARAM, "认证模式无效", c)
		return
	}

	// 验证必需参数
	if req.RequiresPhoto() && req.PhotoEncData == "" {
		response.FailWithErrcode(response.E_PARAM, "人脸认证模式需要提供照片数据", c)
		return
	}

	if req.RequiresPassword() && req.CertPwdData == "" {
		response.FailWithErrcode(response.E_PARAM, "口令认证模式需要提供口令数据", c)
		return
	}

	if req.RequiresCert() && req.Cert == "" {
		response.FailWithErrcode(response.E_PARAM, "返回年龄标识模式需要提供加密证书", c)
		return
	}

	// 获取客户端IP和应用ID
	ip := GetIp(c)
	appid := GetAppid(c)

	// 处理认证请求
	data, err := service.ProcessCtdidAuth(&req, ip, appid)
	if err != nil {
		global.LOG.Error("网络身份认证处理失败", zap.Error(err))
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}

	// 记录日志
	global.LOG.Info("网络身份认证请求",
		zap.String("bizSeq", req.BizSeq),
		zap.String("mode", req.Mode),
		zap.String("ip", ip))

	// 返回响应
	response.OkWithData(data, c)
}

// CtdidTestTLS 测试TLS连接配置
// @Summary 测试TLS连接配置
// @Description 测试客户端证书和TLS连接是否正确配置
// @Tags 网络身份认证
// @Accept json
// @Produce json
// @Success 200 {object} gin.H "测试结果"
// @Router /ctdid/test-tls [get]
func CtdidTestTLS(c *gin.Context) {
	// 测试TLS连接
	err := service.TestTLSConnection()
	if err != nil {
		global.LOG.Error("TLS连接测试失败", zap.Error(err))
		response.FailWithErrcode(response.E_ACCESS, "TLS连接测试失败: "+err.Error(), c)
		return
	}

	// 返回成功响应
	response.OkWithData(gin.H{
		"message": "TLS连接测试成功",
		"status":  "ok",
	}, c)
}
