package model

import "time"

type AcDevicesUniq struct {
	ID          uint      `gorm:"primarykey"`
	CreatedAt   time.Time `gorm:"column:createat;type:timestamp"`
	Uniqno      string
	Model       string
	Origin      string
	Province    string
	City        string
	Area        string
	Subarea     string
	MesExists   int `gorm:"default:-1;comment:'是否存在于mes中，-1 未判断，0 否，1 是'"`
	Exchanged   int `gorm:"default-1;comment:'是否为换货机，-1 未判断，0 否，1 是，2 未找到'"`
	Stored      int `gorm:"default:-1;comment:'是否入库，-1 未判断，0 否，1 是'"`
	Status      int `gorm:"default:0;comment:'翻新状态，-1 未判断，0 否，1 是'"`
	Firstbinder int
	Bindat      time.Time `gorm:"type:timestamp"`
	Location    string
}

func (m AcDevicesUniq) TableName() string {
	return "ac_devices_uniq"
}
