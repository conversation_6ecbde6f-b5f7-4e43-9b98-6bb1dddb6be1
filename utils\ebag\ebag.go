package ebag

import (
	"account/global"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"
)

type Resp struct {
	FResponseData json.RawMessage `json:"F_responseData"`
	FResponseMsg  string          `json:"F_responseMsg"`
	FResponseNo   int64           `json:"F_responseNo"`
}

func SendJson(url string, data interface{}) (resp []byte, err error) {
	buf, err := json.Marshal(data)
	if err != nil {
		return
	}
	r := strings.NewReader(string(buf))
	client := http.Client{Timeout: time.Duration(10) * time.Second}
	response, err := client.Post(url, "application/json", r)
	if err != nil {
		return
	}
	defer response.Body.Close()
	resp, err = ioutil.ReadAll(response.Body)

	var res Resp
	if err = json.Unmarshal(resp, &res); err != nil {
		return
	}
	global.LOG.Info("ebag_request", zap.String("req_uri", url), zap.String("req_body", string(buf)),
		zap.Int64("resp_no", res.FResponseNo), zap.String("resp_msg", res.FResponseMsg))
	if res.FResponseNo != 10000 {
		err = fmt.Errorf("错误(%d)：%s", res.FResponseNo, res.FResponseMsg)
		return
	}
	resp = []byte(res.FResponseData)
	return
}
