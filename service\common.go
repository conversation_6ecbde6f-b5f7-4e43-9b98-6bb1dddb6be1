package service

import (
	"account/global"
	"account/model"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis"
)

func SetRedisMember(m *model.AcMember) {
	if global.Redis == nil {
		return
	}
	if m.Uid > 0 {
		key := "account:members:" + strconv.Itoa(int(m.Uid))
		if buf, _ := json.Marshal(m); len(buf) > 0 {
			global.Redis.Set(key, string(buf), time.Hour*24*3)
		}
	}
}

func DelRedisMember(uids ...int) error {
	if global.Redis == nil {
		return redis.Nil
	}
	keys := make([]string, 0, len(uids))
	for _, uid := range uids {
		keys = append(keys, "account:members:"+strconv.Itoa(uid))
	}
	_, err := global.Redis.Del(keys...).Result()
	return err
}

func GetRedisMember(uid string) (*model.AcMember, error) {
	if global.Redis == nil {
		return nil, redis.Nil
	}
	uid = strings.TrimPrefix(uid, "0")
	key := "account:members:" + uid
	str, err := global.Redis.Get(key).Result()
	var m model.AcMember
	if err == nil {
		json.Unmarshal([]byte(str), &m)
		return &m, nil
	}
	return nil, err
}

func GetRedisDevice(uniq string) (*model.AcDevice, error) {
	if global.Redis == nil {
		return nil, redis.Nil
	}
	key := "account:device:" + uniq
	str, err := global.Redis.Get(key).Result()
	var d model.AcDevice
	if err == nil {
		json.Unmarshal([]byte(str), &d)
		return &d, nil
	}
	return nil, err
}

func SetRedisDevice(d model.AcDevice) {
	if global.Redis == nil {
		return
	}
	if d.ID > 0 {
		key := "account:device:" + d.Uniqno
		if buf, _ := json.Marshal(d); len(buf) > 0 {
			global.Redis.Set(key, string(buf), time.Hour*24*7)
		}
	}
}
