package request

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"account/global"
	"account/model"
	"account/utils"
	"account/utils/ebag"
)

type UserInfo struct {
	CardNo   string // 身份证号
	Username string // 真实姓名
	Sex      string // 性别。取值：男，女
	Mobile   string
	School   string
	RawData  json.RawMessage
}

func (gm *UserInfo) ToAcMember(ip string, regapp string) *model.AcMember {
	now := time.Now().Unix()
	return &model.AcMember{
		Username:     "card-" + utils.MD5(gm.CardNo),
		Regip:        ip,
		Regdate:      time.Now().Unix(),
		Regapp:       regapp,
		AccessToken:  utils.MD5(strconv.FormatInt(now, 10) + gm.CardNo),
		AccessExpire: now + global.TOKEN_EXPIRE,
	}
}

func (gm *UserInfo) ToUserProfile(uid int) *model.UserProfile {
	birth, _ := strconv.Atoi(gm.CardNo[6:14])
	y := birth / 10000
	m := birth / 100 % 100
	d := birth % 100
	var gender uint
	switch gm.Sex {
	case "男":
		gender = 0
	case "女":
		gender = 1
	default:
		if (gm.CardNo[16]-'0')%2 == 0 {
			gender = 1
		}
	}
	district, _ := strconv.Atoi(gm.CardNo[:6])
	province, city := district/10000, district/100
	return &model.UserProfile{
		Uid:        uid,
		Username:   "card-" + utils.MD5(gm.CardNo),
		Realname:   gm.Username,
		Gender:     gender,
		BirthY:     y,
		BirthM:     m,
		BirthD:     d,
		ProvinceId: uint(province),
		CityId:     uint(city),
		DistrictId: uint(district),
		School:     gm.School,
	}
}

func (gm *UserInfo) ToUserProfileExtra(uid uint) *model.UserProfileExtra {
	var gender uint
	switch gm.Sex {
	case "男":
		gender = 0
	case "女":
		gender = 1
	default:
		if (gm.CardNo[16]-'0')%2 == 0 {
			gender = 1
		}
	}
	return &model.UserProfileExtra{
		Uid:      uid,
		CardNo:   gm.CardNo,
		Username: gm.Username,
		Gender:   gender,
		School:   gm.School,
		RawData:  string(gm.RawData),
	}
}

func NewMembersFormZhujiUser(users []ebag.ZhujiUserInfo) (list []UserInfo) {
	list = make([]UserInfo, 0, len(users))
	for _, u := range users {
		list = append(list, UserInfo{
			CardNo:   u.CardNo,
			Username: u.Username,
			Sex:      u.Gender,
			Mobile:   u.Mobile,
			School:   u.School,
			RawData:  json.RawMessage(u.RawData),
		})
	}
	return
}

// CardNo 身份证结构
/*
	组成：6位地区编码
*/
type CardNo struct {
	CardNo string
	no     [18]uint8
}

func NewCardNo(no string) *CardNo {
	cn := &CardNo{
		CardNo: no,
		no:     [18]uint8{},
	}
	sum := 0
	for i := 0; i < 18; i++ {
		switch {
		case no[i] >= '0' && no[i] <= '9':
			cn.no[i] = uint8(no[i] - '0')
		case no[i] == 'x' || no[i] == 'X' && i == 17:
			cn.no[i] = 10
		default:
			return nil
		}
		// 每一位与权重相乘
		sum += int(cn.no[i]) * _card[i]
	}
	if (sum)%11 != 1 {
		fmt.Println(no)
		fmt.Println(sum % 11)
		return nil
	}
	return cn
}

// 身份证号校验权重
var _card = [18]int{7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1}

func (cn *CardNo) GetBirthY() (year int) {
	for i := 6; i < 10; i++ {
		year = year*10 + int(cn.no[i])
	}
	return
}

func (cn *CardNo) GetBirthM() (month int) {
	for i := 10; i < 12; i++ {
		month = month*10 + int(cn.no[i])
	}
	return
}

func (cn *CardNo) GetBirthD() (day int) {
	for i := 12; i < 14; i++ {
		day = day*10 + int(cn.no[i])
	}
	return
}
