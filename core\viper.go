package core

import (
	"account/global"
	_ "account/packfile"
	"flag"
	"fmt"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

func Viper() *viper.Viper {
	var config string
	flag.StringVar(&config, "c", "", "choose config file.")
	flag.Parse()
	if config == "" { // 优先级: 命令行 > 默认值
		config = "config.yaml"
		fmt.Printf("您正在使用config的默认值,config的路径为%v\n", config)
	} else {
		fmt.Printf("您正在使用命令行的-c参数传递的值,config的路径为%v\n", config)
	}

	v := viper.New()
	v.SetConfigFile(config)
	err := v.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("Fatal error config file: %s \n", err))
	}
	v.WatchConfig()

	v.OnConfigChange(func(e fsnotify.Event) {
		fmt.Println("config file changed:", e.Name)
		if err := v.Unmarshal(&global.CONFIG); err != nil {
			fmt.Println(err)
		}
		global.Filter.UpdateNoisePattern(v.GetString("filter.noise"))
		Apis(v)
	})
	if err := v.Unmarshal(&global.CONFIG); err != nil {
		fmt.Println(err)
	}
	initAppsec()
	return v
}

func initAppsec() {
	if appsec_path := global.CONFIG.App.AppSecPath; appsec_path != "" {
		vp := viper.New()
		vp.SetConfigFile(appsec_path)
		if err1 := vp.ReadInConfig(); err1 == nil {
			global.SetApps(vp.GetStringMapString("apps"))
		}
		vp.WatchConfig()
		vp.OnConfigChange(func(e fsnotify.Event) {
			fmt.Println("appsec file changed")
			if err := vp.ReadInConfig(); err == nil {
				global.SetApps(vp.GetStringMapString("apps"))
			}
		})
	}
}
