# coding=utf8
from mq_http_sdk.mq_exception import MQExceptionBase
from mq_http_sdk.mq_consumer import *
from mq_http_sdk.mq_client import *
import yaml
import time
from logger import log
from handlers.noop_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from handlers.sync_handler import S<PERSON><PERSON>andler
from handlers.location_handler import LocationHandler

handlers = [Noop<PERSON><PERSON><PERSON>(), SyncHandler(), LocationHandler()]

def mq_subscribe(config_path='config.yaml'):
    cfg = yaml.load(open(config_path,'r',encoding='utf-8').read(),Loader=yaml.BaseLoader)
    #print(cfg)
    mq_client = MQClient(cfg['host'], cfg['access_key'], cfg['secret_key'])
    return mq_client.get_consumer(cfg['instance_id'], cfg['topic'], cfg['group_id'])
    
def mq_consume(msg):
    #消息消费逻辑
    try:
        data = json.loads(msg.message_body)
        #print(data)
        for handler in handlers:
            handler.handle(msg.message_id, msg.message_tag, data)
    except Exception as e:
        log(e)
    
def loop():
    consumer = mq_subscribe()
    log("Start working...")
    
    while True:
        try:
            #长轮询消费消息
            recv_msgs = consumer.consume_message(4, 5)
            for msg in recv_msgs:
                mq_consume(msg)
        except MQExceptionBase as e:
            if e.type == "MessageNotExist":
                continue
            log("Exception:%s" % e)
            time.sleep(5)
            continue

        #msg.next_consume_time 前若不确认消息消费成功，则消息会重复消费
        #消息句柄有时间戳，同一条消息每次消费拿到的都不一样
        try:
            receipt_handle_list = [msg.receipt_handle for msg in recv_msgs]
            consumer.ack_message(receipt_handle_list)
            #print ("[%s] Ak Succeed: %s" % (int(time.time()),len(receipt_handle_list)))
        except MQExceptionBase as e:
            log ("[%s] Ak Fail: %s" % (int(time.time()), e))
            #某些消息的句柄可能超时了会导致确认不成功
            if e.sub_errors:
              for sub_error in e.sub_errors:
                log ("\tErrorHandle:%s,ErrorCode:%s,ErrorMsg:%s" % (sub_error["ReceiptHandle"], sub_error["ErrorCode"], sub_error["ErrorMessage"]))
                
if __name__ == '__main__':
    loop()