package v1

import (
	"account/global"
	"account/model/response"
	"account/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PutAvatar 上传用户头像【无需登录】
// login no
// @Param sn string true "签名"
// @Param id int true "用户id"
// @Param avatar | image file true "头像文件"
// @Success {json} json {"status": 200, "avatar": ""}
// @Router /put/avatar [post]
func PutAvatar(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	id := GetRequestParam(c, "id")
	uid, _ := strconv.Atoi(id)
	if uid == 0 {
		response.FailWithErrcode(response.E_PARAM, "参数错误", c)
		return
	}
	avatar, err := c.FormFile("avatar")
	if err != nil {
		avatar, err = c.FormFile("image")
	}
	if err != nil {
		response.FailWithErrcode(response.E_PARAM, err.Error(), c)
		return
	}
	if avatar.Size > 2097152 || avatar.Size == 0 {
		response.FailWithErrcode(response.E_PARAM, "文件大小错误", c)
		return
	}
	mimetype := avatar.Header.Get("Content-Type")
	if mimetype != "image/png" && mimetype != "image/jpeg" && mimetype != "image/pjpeg" {
		response.FailWithErrcode(response.E_PARAM, "文件格式错误"+mimetype, c)
		return
	}
	dst := global.CONFIG.App.AvatarDir + "/" + id
	fd, err := avatar.Open()
	if err != nil {
		response.FailWithErrcode(response.E_UNKNOWN, "临时文件打开失败", c)
		return
	}
	defer fd.Close()
	if err := utils.UploadFile(fd, dst); err != nil {
		global.LOG.Debug("头像上传失败", zap.Int("uid", uid), zap.Error(err))
		response.FailWithErrcode(response.E_UNKNOWN, "文件上传失败", c)
	} else {
		response.OkWithData(gin.H{"status": 200, "avatar": utils.GetAvatarUri(uint(uid))}, c)
	}
}

// PutVideo 穿戴设备上传图片
// login no
// @Param sn string true "签名"
// @Param image file true "图片文件"
// @Router /put/image [post]
func PutImage(c *gin.Context) {
	if reason, _, _ := CheckSignature(c); reason < 0 {
		response.FailWithErrcode(response.E_INVALID_SN, "", c)
		return
	}
	dir := "wear/p"
	t := time.Now().Format("20060102150405")
	id := t + utils.Uniqid()
	put_uploadOss(c, dir, id, "image", 1)
}

// PutVideo 穿戴设备上传音频
// login no
// @Param sn string true "签名"
// @Param audio file true "音频文件"
// @Router /put/audio [post]
func PutAudio(c *gin.Context) {
	if reason, _, _ := CheckSignature(c); reason < 0 {
		response.FailWithErrcode(response.E_INVALID_SN, "", c)
		return
	}
	dir := "wear/a"
	t := time.Now().Format("20060102150405")
	id := t + utils.Uniqid()
	put_uploadOss(c, dir, id, "audio", 2)
}

// PutVideo 穿戴设备上传视频
// login no
// @Param sn string true "签名"
// @Param video file true "视频文件"
// @Router /put/video [post]
func PutVideo(c *gin.Context) {
	if reason, _, _ := CheckSignature(c); reason < 0 {
		response.FailWithErrcode(response.E_INVALID_SN, "", c)
		return
	}
	dir := "wear/v"
	t := time.Now().Format("20060102150405")
	id := t + utils.Uniqid()
	put_uploadOss(c, dir, id, "video", 3)
}

// 上传 FormFile["image"] 到 oss, dst = dir/id + suffix
// @param filetype 上传文件类型。1 图片；2 语音；3 视频
// @Succcess {json} json {"status": 200, "name": id + suffix, "filename": avatarHost/dst}
func put_uploadOss(c *gin.Context, dir, id string, form_filename string, filetype int) {
	file, err := c.FormFile(form_filename)
	if err != nil {
		response.FailWithErrcode(response.E_PARAM, "未指定文件名称", c)
		return
	}
	if file.Size == 0 || file.Size >= 2097152 {
		response.FailWithErrcode(response.E_PARAM, "文件大小错误", c)
		return
	}
	mimetype := file.Header.Get("Content-Type")
	suffix := getPutFileSuffix(filetype, mimetype)
	if suffix == "" {
		response.FailWithErrcode(response.E_PARAM, "文件格式错误"+mimetype, c)
		return
	}
	fd, err := file.Open()
	if err != nil {
		response.FailWithErrcode(response.E_UNKNOWN, "临时文件打开失败", c)
		return
	}
	defer fd.Close()
	dst := dir + "/" + id + suffix
	if err := utils.UploadFile(fd, dst); err != nil {
		global.LOG.Debug("文件上传失败", zap.Error(err))
		response.FailWithErrcode(response.E_UNKNOWN, "上传失败", c)
	} else {
		response.OkWithData(gin.H{
			"status": 200, "name": id + suffix,
			"filename": global.CONFIG.App.AvatarDir + "/" + dst,
		}, c)
	}
}

func getPutFileSuffix(filetype int, mimetype string) string {
	switch filetype {
	case 1: // image
		switch mimetype {
		case "image/png":
			return ".png"
		case "image/jpeg", "image/pjpeg":
			return ".jpg"
		}
	case 2: // audio
		switch mimetype {
		case "audio/amr":
			return ".amr"
		case "audio/mp3":
			return ".mp3"
		case "audio/wav":
			return ".wav"
		}
	case 3: // video
		switch mimetype {
		case "video/mp4":
			return ".mp4"
		case "video/avi":
			return ".avi"
		}
	}
	return ""
}
