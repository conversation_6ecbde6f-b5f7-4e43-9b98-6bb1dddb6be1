package cryptor

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
)

func AesCbcEncrypt(data, key string) string {
	block, _ := aes.NewCipher([]byte(key))
	buf := pkcs7Padding([]byte(data), block.BlockSize())

	encrypted := make([]byte, aes.BlockSize+len(buf))
	iv := encrypted[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		panic(err)
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(encrypted[aes.BlockSize:], buf)

	return base64.URLEncoding.EncodeToString(encrypted)
}

func AesCbcDecrypt(encrypted, key string) string {
	block, _ := aes.NewCipher([]byte(key))
	buf, err := base64.URLEncoding.DecodeString(encrypted)
	if err != nil || len(buf) < aes.BlockSize {
		fmt.Println(err)
		return ""
	}
	iv := buf[:aes.BlockSize]
	buf = buf[aes.BlockSize:]
	if len(buf) == 0 || len(buf)%aes.BlockSize != 0 {
		return ""
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(buf, buf)

	decrypted := pkcs7UnPadding(buf)
	return string(decrypted)
}
