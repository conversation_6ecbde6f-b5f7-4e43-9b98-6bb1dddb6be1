package utils

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
)

func SendPostForm(url string, data url.Values) (resp []byte, err error) {
	client := http.Client{Timeout: time.Duration(10) * time.Second} //创建客户端
	response, err := client.PostForm(url, data)
	if err != nil {
		return
	}
	defer response.Body.Close()
	resp, err = ioutil.ReadAll(response.Body)
	return
}

func SendGet(url string) (resp []byte, err error) {
	client := http.Client{Timeout: time.Duration(10) * time.Second}
	response, err := client.Get(url)
	if err != nil {
		return
	}
	defer response.Body.Close()
	resp, err = ioutil.ReadAll(response.Body)
	return
}

func SendJson(url string, data interface{}) (resp []byte, err error) {
	buf, err := json.Marshal(data)
	if err != nil {
		return
	}
	r := strings.NewReader(string(buf))
	client := http.Client{Timeout: time.Duration(10) * time.Second}
	response, err := client.Post(url, "application/json", r)
	if err != nil {
		return
	}
	defer response.Body.Close()
	resp, err = ioutil.ReadAll(response.Body)
	return
}
