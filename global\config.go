package global

import (
	"context"

	"account/config"
	"account/pkg/sensitive"

	"github.com/go-redis/redis"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var CONFIG config.Server

var (
	VIPER  *viper.Viper
	LOG    *zap.Logger
	DB     *gorm.DB
	Redis  *redis.Client
	Filter *sensitive.Filter
)

type ctxKey string

const (
	_dbCtxKey = ctxKey("db")
)

func GetDB(c context.Context) *gorm.DB {
	db, _ := c.Value(_dbCtxKey).(*gorm.DB)
	if db == nil {
		db = DB.WithContext(c)
	}
	return db
}

func InjectDB(c context.Context, db *gorm.DB) context.Context {
	return context.WithValue(c, _dbCtxKey, db)
}
