package v1

import (
	"account/global"
	"account/model"
	"account/model/request"
	"account/model/response"
	"account/service"
	"errors"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ZtLogin 中台1.0账号登录
func ZtLogin(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	var req *request.ZtLoginReq
	if err := c.ShouldBind(&req); err != nil {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	req.Ip = GetIp(c)
	req.Regapp = GetAppid(c)
	data, err := service.ZtRegister(c, req)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	now := time.Now().Unix()
	var m *model.AcMember
	if data.AccessExpire <= now {
		m, err = service.GetMemberByUid(strconv.Itoa(int(data.Uid)))
		if err != nil {
			response.FailWithError(err, c)
		}
		data.AccessToken = service.CreateMemberAccessToken(data.Uid, now)
		data.AccessExpire = now + global.TOKEN_EXPIRE
		if m != nil && m.Uid > 0 {
			m.AccessToken = data.AccessToken
			m.AccessExpire = data.AccessExpire
			service.SaveMemberAccess(m)
			c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile, "regdate": m.Regdate})
		}
	}
	response.OkWithData(gin.H{"data": data}, c)
}

func ZtCheck(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	var req *request.ZtCheckReq
	if err := c.ShouldBind(&req); err != nil {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	data, err := service.ZtCheck(c, req)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithError(err, c)
		}
		return
	}
	response.OkWithData(gin.H{"data": data}, c)
}
