package router

import (
	v1 "account/controller/v1"

	"github.com/gin-gonic/gin"
)

func InitEmailRouter(Router *gin.RouterGroup) {
	ApiRouter := Router.Group("email")
	{
		Any(ApiRouter, "register", v1.EmailRegister)
		Any(ApiRouter, "verify", v1.EmailVerify)
		Any(ApiRouter, "bind", v1.EmailBind)
		Any(ApiRouter, "rebind", v1.EmailRebind)
		Any(ApiRouter, "resetpwd", v1.EmailResetPassword)
		Any(ApiRouter, "reactivate", v1.EmailReActivate)
	}
}
