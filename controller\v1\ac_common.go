package v1

import (
	"errors"
	"path"
	"strconv"
	"strings"
	"time"

	"account/global"
	"account/model"
	"account/model/response"
	"account/pkg/cryptor"
	"account/service"
	"account/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	UID_LENGTH      = 8
	TIME_LENGTH     = 10
	MD5_LENGTH      = 32
	SIGN_MIN_LENGTH = 51
)

// CheckSignature 通用签名检查
func CheckSignature(c *gin.Context) (reason int, desp string, member *model.AcMember) {
	global.AppsRWMutex.RLock()
	defer global.AppsRWMutex.RUnlock()
	return checkSignature2(c, global.SN_EXPIRE, "", global.Apps)
}

// checkSignature 签名检查
//
// -1 bad signature，签名格式错误
//
// -2 token expired，用户token过期
//
// -3 bad appid，未知appid
//
// -4 sn expired，签名过期
//
// -5 invalid uid，账号不存在或数据库错误
//
// -6 checkSN error【未使用】
//
// 0 not login but ok，未登录，签名正确
//
// 1 login，已登录
func checkSignature(c *gin.Context, expire int64, apps map[string]string) (reason int, desp string, member *model.AcMember) {
	sn := GetRequestParam(c, "sn")
	if len(sn) < SIGN_MIN_LENGTH {
		reason = -1
		desp = "签名格式错误"
		return
	}
	uid := sn[0:UID_LENGTH]
	timestr := sn[UID_LENGTH : UID_LENGTH+TIME_LENGTH]
	timestamp, _ := strconv.ParseInt(timestr, 10, 64)
	md5str := sn[UID_LENGTH+TIME_LENGTH : UID_LENGTH+TIME_LENGTH+MD5_LENGTH]
	appid := sn[UID_LENGTH+TIME_LENGTH+MD5_LENGTH:]
	c.Set("userid", uid)
	c.Set("timestamp", timestamp)
	c.Set("digest", md5str)
	c.Set("appid", appid)
	now := time.Now().Unix()
	if elapsed := now - timestamp; elapsed < -300 || elapsed > expire {
		reason = -4
		desp = "签名无效：请检查您的系统时间"
		return
	}
	appsec, ok := apps[strings.ToLower(appid)]
	if !ok {
		reason = -3
		return
	}
	c.Set("appsec", appsec)
	if uid == "00000000" {
		token := utils.MD5(appid)
		s := utils.MD5(timestr + appsec + token)
		if s != md5str {
			reason = -1
			desp = "签名不正确"
			return
		}
		reason = 0
		return
	}
	m, err := service.GetMemberByUid(uid)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			global.LOG.Debug("数据库异常", zap.Error(err))
		}
		reason = -5
		return
	}
	token := m.AccessToken
	s := utils.MD5(timestr + appsec + token)
	if s != md5str {
		reason = -1
		desp = "签名不正确"
		return
	}
	if now > m.AccessExpire {
		reason = -2
		return
	}
	c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile, "regdate": m.Regdate})
	reason = 1
	member = m
	return
}

// GetRequestParam 从query或form中获取指定参数
func GetRequestParam(c *gin.Context, name string) string {
	if result := c.Query(name); result != "" {
		return result
	} else {
		return c.PostForm(name)
	}
}

// GetAppid 从context中获取appid
func GetAppid(c *gin.Context) string {
	val, _ := c.Get("appid")
	appid, _ := val.(string)
	return appid
}

func GetIp(c *gin.Context) string {
	if val, ok := c.Get("ip"); ok {
		return val.(string)
	} else {
		ip := c.ClientIP()
		c.Set("ip", ip)
		return ip
	}
}

func ServerInfo(c *gin.Context) {
	app := global.CONFIG.App
	response.OkWithData(gin.H{
		"serverName":    app.ServerName,
		"serverVersion": app.ServerVersion,
		"lastModified":  app.LastModified,
		"domain":        app.Domain,
		"avatarHost":    path.Join(app.AvatarHost, app.AvatarDir),
		"serverTime":    time.Now().Unix(),
	}, c)
}

func RouteNoFound(c *gin.Context) {
	response.FailWithErrcode(response.E_ACCESS, "", c)
}

// getMemberLoginInfo 获取登录成功待返回的信息
func getMemberLoginInfo(m *model.AcMember) gin.H {
	return gin.H{
		"uid":           strconv.Itoa(int(m.Uid)),
		"username":      m.Username,
		"forbidden":     strconv.Itoa(m.Forbidden),
		"access_token":  m.AccessToken,
		"access_expire": strconv.FormatInt(m.AccessExpire, 10),
	}
}

func GetDeviceSerial(c *gin.Context) (serial string) {
	m, ok := c.Get("dinfo")
	if !ok {
		return
	}
	dinfo := m.(gin.H)
	serial, _ = dinfo["serial"].(string)
	return
}

func GetDeviceModel(c *gin.Context) (deviceModel string) {
	m, ok := c.Get("dinfo")
	if !ok {
		return
	}
	dinfo := m.(gin.H)
	deviceModel, _ = dinfo["model"].(string)
	return
}

func ParseSignature(c *gin.Context) (reason int, desp string) {
	sn := GetRequestParam(c, "sn")
	if len(sn) < SIGN_MIN_LENGTH {
		reason = -1
		desp = "签名格式错误"
		return
	}
	exists, appidF := global.AppFilter.FindIn(strings.ToLower(sn))
	if !exists {
		reason = -1
		desp = "签名格式错误"
		return
	}
	appid := sn[len(sn)-len(appidF):]
	sn = sn[0 : len(sn)-len(appidF)]

	md5str := sn[len(sn)-MD5_LENGTH:]
	sn = sn[0 : len(sn)-MD5_LENGTH]

	timestr := sn[len(sn)-TIME_LENGTH:]
	sn = sn[0 : len(sn)-TIME_LENGTH]
	timestamp, _ := strconv.ParseInt(timestr, 10, 64)

	uidstr := sn[:]

	c.Set("userid", uidstr)
	c.Set("timestamp", timestamp)
	c.Set("digest", md5str)
	c.Set("appid", appid)
	uid, _ := strconv.Atoi(uidstr)
	c.Set("uid", uid)
	return
}

func checkSignature2(c *gin.Context, expire int64, checkstr string, apps map[string]string) (reason int, desp string, member *model.AcMember) {
	reason, desp = ParseSignature(c)
	if reason < 0 {
		return
	}
	if len(apps) == 0 {
		global.AppsRWMutex.RLock()
		defer global.AppsRWMutex.RUnlock()
		apps = global.Apps
	}
	appid := c.GetString("appid")
	appsec := apps[strings.ToLower(appid)]
	if appid == "" || appsec == "" {
		reason = -3
		return
	}
	c.Set("appsec", appsec)
	now := time.Now().Unix()
	timestamp := c.GetInt64("timestamp")
	if expire == 0 {
		expire = global.SN_EXPIRE
	}
	if elapsed := now - timestamp; elapsed < -300 || elapsed > expire {
		reason = -4
		desp = "签名无效：请检查您的系统时间"
		return
	}
	if checkstr == "" {
		if uid := c.GetInt("uid"); uid == 0 {
			checkstr = utils.MD5(appid)
		} else {
			var err error
			member, err = service.GetMemberByUid(c.GetString("userid"))
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					global.LOG.Debug("数据库异常", zap.Error(err))
				}
				reason = -5
				return
			}
			checkstr = member.AccessToken
		}
	}
	sn := utils.MD5(strconv.FormatInt(timestamp, 10) + appsec + checkstr)
	if sn != c.GetString("digest") {
		reason = -1
		desp = "签名不正确"
		return
	}
	if member != nil {
		if now > member.AccessExpire {
			reason = -2
			return
		}
		c.Set("uinfo", gin.H{"uid": member.Uid, "mobile": member.Mobile, "regdate": member.Regdate})
		reason = 1
	}
	return
}

func DecryptParam(c *gin.Context, param string) (data string) {
	key := c.GetString("appsec")
	if len(key) == 0 {
		return
	}
	if len(key) != 32 {
		key = utils.MD5(key)
	}
	return cryptor.AesCbcDecrypt(param, key)
}
