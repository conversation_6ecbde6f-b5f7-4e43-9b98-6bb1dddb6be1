package shumei

import (
	"account/utils"
	"strconv"
)

type (
	TextCheckReq struct {
		Req
		Uid     int      `json:"-"`
		EventId string   `json:"eventId"`
		Type    string   `json:"type"` // TEXTRISK
		Data    TextData `json:"data"`
	}
	TextData struct {
		Text     string `json:"text"`
		TokenId  string `json:"tokenId"`
		Nickname string `json:"nickname"`
	}
)

func TextCheck(in *TextCheckReq) (data CheckRes, err error) {
	setupReq(&in.Req)
	in.Type = "TEXTRISK"
	in.EventId = "nickname"
	in.Data.TokenId = utils.MD5(strconv.Itoa(in.Uid))
	_, err = client.R().
		ForceContentType("application/json").
		SetBody(in).
		SetResult(&data).
		SetError(&data).
		Post("/text/v4")
	if err != nil {
		return
	}
	if err = checkResp(&data.Resp); err != nil {
		return
	}
	return
}
