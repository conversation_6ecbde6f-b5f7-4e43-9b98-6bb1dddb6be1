package utils

import (
	"account/global"
	"strings"

	"io"
	"os"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

// UploadFile 上传文件
func UploadFile(r io.Reader, objKey string) error {
	client, _ := oss.New(global.CONFIG.Oss.EndPoint,
		global.CONFIG.Oss.AccessKeyId,
		global.CONFIG.Oss.AccessKeySecret)
	bucket, err := client.Bucket(global.CONFIG.Oss.BucketName)
	if err != nil {
		return err
	}
	return bucket.PutObject(objKey, r)
}

// SaveUploadedFile 拷贝文件到服务器
func SaveUploadedFile(r io.Reader, dst string) error {
	dst = "test/oss/" + dst
	path := ""
	i := strings.LastIndex(dst, "/")
	if i != -1 {
		path = string(dst[:i])
	}
	if path != "" {
		os.MkdirAll(path, 0777)
	}
	//创建 dst 文件
	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()
	// 拷贝文件
	_, err = io.Copy(out, r)
	return err
}
