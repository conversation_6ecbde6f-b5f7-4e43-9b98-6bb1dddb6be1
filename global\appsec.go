package global

import (
	"strings"
	"sync"

	"account/pkg/sensitive"
)

var SystemApps = map[string]string{
	"000000": "Readboy-Account-Center-1.0 201505",
}

var AppsRWMutex = sync.RWMutex{}

var Apps = map[string]string{}

var AppFilter *sensitive.Filter // appid 查找

func SetApps(apps map[string]string) {
	AppsRWMutex.Lock()
	defer AppsRWMutex.Unlock()
	Apps = apps
	if AppFilter == nil {
		AppFilter = sensitive.New()
	}
	for appid := range apps {
		AppFilter.AddWord(strings.ToLower(appid))
	}
}
