package v1

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

/*
	手表家长账号相关逻辑
*/

func wear_checkSn(c *gin.Context) (mobile string, ok bool) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		ok = false
		return
	}
	if GetAppid(c) != "wear.readboy.com" {
		ok = false
		response.FailWithErrcode(response.E_ACCESS_FORBIDDEN, "", c)
		return
	}
	mobile, ok = GetAndCheckMobile(c)
	return
}

// 新手表账号注册
func WearRegister(c *gin.Context) {
	mobile, ok := wear_checkSn(c)
	if !ok {
		return
	}
	pwd, ok := GetAndCheckPassword(c)
	if !ok {
		return
	}
	m, err := service.GetMemberByMobile(mobile)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
	}
	now := time.Now().Unix()
	timestr := strconv.FormatInt(now, 10)
	if m.Uid == 0 {
		m.Username = mobile
		m.Mobile = mobile
		m.Regip = GetIp(c)
		m.Regdate = now
		m.Regapp = GetAppid(c)
		m.Forbidden = 0
		m.AccessToken = utils.MD5(mobile + timestr)
		m.AccessExpire = now + global.TOKEN_EXPIRE
		service.FormatMemberPassword(m, pwd)
		if err := service.CreateMember(m); err != nil {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
	} else if !service.CheckPwd(m, pwd) {
		m.Uid = 0
	}
	response.OkWithData(gin.H{
		"uid": m.Uid,
	}, c)
}

// 新手表账号重置密码
func WearResetPwd(c *gin.Context) {
	mobile, ok := wear_checkSn(c)
	if !ok {
		return
	}
	password, ok := GetAndCheckPassword(c)
	if !ok {
		return
	}
	m, err := service.GetMemberByMobile(mobile)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "该手机未注册", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}

	now := time.Now().Unix()
	service.FormatMemberPassword(m, password)
	m.AccessToken = utils.MD5(mobile + strconv.FormatInt(now, 10))
	m.AccessExpire = now + global.TOKEN_EXPIRE
	err = service.ResetPassword(m)
	if err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	response.OkWithData(gin.H{
		"access_token": m.AccessToken, "access_expire": m.AccessExpire, "status": "success",
	}, c)
}

func WearUnregister(c *gin.Context) {
	mobile, ok := wear_checkSn(c)
	if !ok {
		return
	}
	m, err := service.GetMemberByMobile(mobile)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	service.DeleteProfileByUid(m.Uid)
	if service.DeleteMemberByUid(m.Uid) != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	} else {
		global.LOG.Info("mobile/unregister", zap.Uint("uid", m.Uid), zap.String("mobile", mobile))
		service.NotifyParentManager(int(m.Uid), mobile)
		response.OkWithData(gin.H{"uid": m.Uid, "success": true}, c)
	}
}

func wearLogin(c *gin.Context, m *model.AcMember) {

}

func wearMobileRebind(c *gin.Context, m *model.AcMember) {
	// TODO 检查手表账号中是否存在重绑手机号，若存在，修改用户密码为对应手表账号
}
