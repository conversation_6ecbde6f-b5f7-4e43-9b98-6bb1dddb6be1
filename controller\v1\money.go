package v1

import (
	"account/model/response"
	"account/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 【无moneylog生成，估计已废弃，暂不实现】

// MoneyCharge 充值
func MoneyCharge(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if reason == 0 {
		username := GetRequestParam(c, "username")
		if username == "" {
			response.FailWithErrcode(response.E_REQUIRE_USERNAME, "", c)
			return
		}
		mem, err := service.GetMemberByAccount(username)
		if err != nil {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
			return
		}
		m = mem
	}
	if !CheckMemberForbidden(c, m) {
		return
	}
	amount, _ := strconv.Atoi(GetRequestParam(c, "amount"))
	if amount <= 0 {
		response.FailWithErrcode(response.E_PARAM, "充值金额错误", c)
		return
	}
}
