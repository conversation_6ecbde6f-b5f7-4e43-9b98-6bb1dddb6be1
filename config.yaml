app:
  server-name: "Readboy Account Center"
  server-version:
  last-modified:
  domain: "https://account.readboy.com"
  avatar-host: "http://img.readboy.com"
  avatar-dir: "avatar"
  env: dev
  port: 8000
  enable-mq: true
  appsec-path: "./appsec.yaml"

# zap logger configuration
zap:
  level: 'info'
  format: 'console'
  prefix: '[AC]'
  director: 'log'
  link-name: 'latest_log'
  show-line: true
  encode-level: 'LowercaseColorLevelEncoder'
  stacktrace-key: 'stacktrace'
  log-in-console: true

oss:
  access-key-id: LTAI4FwDPqasaej5kVnqJKea
  access-key-secret: ******************************
  end-point: http://oss-cn-shenzhen.aliyuncs.com
  # end-point: http://oss-cn-shenzhen-internal.aliyuncs.com
  bucket-name: acres

mysql:
  path: 'rds49edhgd0910y6ok82public.mysql.rds.aliyuncs.com'
  db-name: ac_center
  username: user2015_ac
  password: readboy
  config: 'charset=utf8&parseTime=True&loc=Local&timeout=3s&readTimeout=10s&writeTimeout=20s'
  max-idle-conns: 10
  max-open-conns: 500
  max-conn-lifetime: 3600
  log-mode: true
  log-zap: "zap"

mq:
  end-point: "http://****************.mqrest.cn-shenzhen-internal.aliyuncs.com"
  access-key-id: "LTAI4Fn5cptD9sjsJbmUqYew"
  access-key-secret: "******************************"
  instance-id: "MQ_INST_****************_BcCu1ohA"
  topic: "action"

redis: 
  uri: redis://:rSVj!4d65Ju5FbvDCH$<EMAIL>:6379/9

# 敏感词过滤器配置
filter:
  dict: "/data/www/gin-account/dict.txt"
  noise: "[\\|\\s&%$@*]+" # 干扰字符匹配正则

ebag:
  zhuji: https://xzzj.zjjy.xyz/zhuji-education-cube-api/v1/system/user_info

email:
  from: <EMAIL>
  port: 465
  host: smtp.readboy.com
  password: Hello1234

profile_limit:
  model: # 允许每月修改3次昵称的机型
    - "Readboy_K3"
    - "Readboy_K5"
    - "Readboy_K5Plus"
    - "Readboy_K9"
apis:
  zt: 
    host: "http://middleground.readboy.com"
  shumei:
    host: "http://api-img-sh.fengkongcloud.com"
    appid: "default"
    appsec: "Un3NK2aomTTNnw4YTONN"


# 网络身份认证配置示例
# 注意：客户端证书固定路径为当前目录下的 读书郎教育科技有限公司/ 文件夹
# 需要在该目录下放置以下文件：
# - cert.cer (客户端证书)
# - prikey.pem (客户端私钥)
# - downstream_root.cer (CA根证书)
#
# Content-Type 说明：
# 虽然国标文档要求 application/json，但实际测试发现需要使用 application/octet-stream
ctdid:

  # 测试环境接口地址
  test_urls:
    - "https://***********:20002/envelopData"  # 中国移动示例接口

  # 生产环境接口地址
  prod_urls:
    - "https://*************:6444/uentrance/interf/auth/request"   # 中国电信
    - "https://***********:6444/uentrance/interf/auth/request"     # 中国移动
    - "https://*************:6444/uentrance/interf/auth/request"   # 中国联通

  # 请求超时时间（秒）
  timeout: 30

  # 重试次数
  retry_count: 1