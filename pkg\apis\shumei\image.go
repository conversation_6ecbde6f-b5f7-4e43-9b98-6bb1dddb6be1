package shumei

import (
	"account/utils"
	"strconv"
)

type (
	ImageCheckReq struct {
		Req
		Uid     int       `json:"-"`
		EventId string    `json:"eventId"`
		Type    string    `json:"type"`
		Data    ImageData `json:"data"`
	}
	ImageData struct {
		Img     string `json:"img"`
		TokenId string `json:"tokenId"`
	}
)

func ImageCheck(in *ImageCheckReq) (data CheckRes, err error) {
	setupReq(&in.Req)
	in.Type = "POLITY_EROTIC_VIOLENT_ADVERT_QRCODE_IMGTEXTRISK"
	in.EventId = "headlmage"
	in.Data.TokenId = utils.MD5(strconv.Itoa(in.Uid))
	_, err = client.R().
		ForceContentType("application/json").
		SetBody(in).
		SetResult(&data).
		SetError(&data).
		Post("/image/v4")
	if err != nil {
		return
	}
	if err = checkResp(&data.Resp); err != nil {
		return
	}
	return
}
