package v1

import (
	"account/global"
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"
	"math/rand"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func ScanPenRegister(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	serial := GetDeviceSerial(c)
	if serial == "" {
		response.FailWithErrcode(response.E_ACCESS_FORBIDDEN, "", c)
		return
	}
	appid := GetAppid(c)
	ns, _ := uuid.FromString(utils.MD5(serial))
	username := uuid.NewV5(ns, appid).String()

	m, err := service.GetMemberByUsername(username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			password := GetRequestParam(c, "password")
			if password == "" {
				password = strconv.Itoa(rand.Intn(********))
			}
			now := time.Now().Unix()
			timestr := strconv.FormatInt(now, 10)
			m.Username = username
			service.FormatMemberPassword(m, password)
			m.Forbidden = 0
			m.AccessToken = utils.MD5(username + timestr)
			m.AccessExpire = now + global.TOKEN_EXPIRE
			m.Regip = GetIp(c)
			m.Regdate = now
			m.Regapp = appid
			err = service.CreateMember(m)
		}
		if err != nil {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
	}
	c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile, "regdate": m.Regdate})
	response.OkWithData(getMemberLoginInfo(m), c)
}

func ScanPenCheck(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	serial := GetDeviceSerial(c)
	if serial == "" {
		response.FailWithErrcode(response.E_ACCESS_FORBIDDEN, "", c)
		return
	}
	appid := GetAppid(c)
	ns, _ := uuid.FromString(utils.MD5(serial))
	username := uuid.NewV5(ns, appid).String()
	m, err := service.GetMemberByUsername(username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	m.AccessExpire = time.Now().Unix() + global.TOKEN_EXPIRE
	if err = service.SaveMemberAccess(m); err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	} else {
		c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile, "regdate": m.Regdate})
		response.OkWithData(getMemberLoginInfo(m), c)
	}
}
