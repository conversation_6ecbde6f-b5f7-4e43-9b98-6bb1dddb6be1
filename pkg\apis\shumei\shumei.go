package shumei

import (
	"account/pkg/apis"
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/viper"
)

var client = resty.New().SetDebug(true).SetDebugBodyLimit(1e5).SetTimeout(5 * time.Second)
var cfg apis.Config

func SetConfig(v *viper.Viper) {
	var c apis.Config
	if v.Unmarshal(&c) != nil {
		return
	}
	if c.Host != "" && c.Host != cfg.Host {
		cfg.Host = c.Host
		client.SetBaseURL(cfg.Host)
	}
	if c.Appid != "" && c.Appid != cfg.Appid {
		cfg.Appid = c.Appid
	}
	if c.Appsec != "" && c.Appsec != cfg.Appsec {
		cfg.Appsec = c.Appsec
	}
}

type Req struct {
	AppId     string `json:"appId"`
	AccessKey string `json:"accessKey"`
}
type Resp struct {
	Code    int    `json:"code"`
	Message string `json:"message,omitempty"`
	body    string `json:"-"`
}

func checkResp(resp *Resp) (err error) {
	if resp.Code != 1100 {
		err = fmt.Errorf("错误（%d）：%s", resp.Code, resp.Message)
		return
	}
	return nil
}

func setupReq(in *Req) {
	in.AppId = cfg.Appid
	in.AccessKey = cfg.Appsec
}

type CheckRes struct {
	Resp
	FinalResult     int64  `json:"finalResult"` // 是否最终结果。1-是
	ResultType      int64  `json:"resultType"`  // 是否人工审核。1-是
	RiskLevel       string `json:"riskLevel"`
	RiskLabel1      string `json:"riskLabel1"`      // 一级风险标签。当riskLevel为PASS时返回normal
	RiskLabel2      string `json:"riskLabel2"`      // 二级风险标签，当riskLevel为PASS时为空
	RiskLabel3      string `json:"riskLabel3"`      // 三级风险标签，当riskLevel为PASS时为空
	RiskDescription string `json:"riskDescription"` // 当riskLevel为PASS时为正常
}

type RiskLevel string

const (
	RiskLevelPass   RiskLevel = "PASS"   // 放行
	RiskLevelReview RiskLevel = "REVIEW" // 建议人工审核
	RiskLevelReject RiskLevel = "REJECT" // 违规
)

func (r *CheckRes) IsPass() bool {
	return strings.ToUpper(r.RiskLevel) == string(RiskLevelPass)
}
