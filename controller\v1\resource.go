package v1

import (
	"account/global"
	"account/model/response"
	"account/utils"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

/*
手指图片上传接口：
POST https://account.readboy.com/resource/finger

params:
  sn: 签名：md5("7d951963de78" + x + "," + y)
  x: x 坐标
  y: y 坐标
  data: 以 multipart 方式上传图片文件

success: json {"status": 200, "name": id + suffix}
*/

// resource_checkSN 获取并检查 sn, x, y 参数
func resource_checkSN(c *gin.Context) (x, y string, ok bool) {
	sn := GetRequestParam(c, "sn")
	x = GetRequestParam(c, "x")
	y = GetRequestParam(c, "y")
	if sn == "" || x == "" || y == "" {
		response.FailWithErrcode(response.E_PARAM, "参数错误", c)
		return
	}
	// if sn != utils.MD5("7d951963de78"+x+","+"y") {
	// 	return
	// }
	ok = true
	return
}

// ResourceFinger
// @Param sn string true "签名"
// @Param x int true "x 坐标"
// @Param y int true "y 坐标"
// @Param sx int false "sx 坐标"
// @Param sy int false "sy 坐标"
// @Router /resource/finger [post]
func ResourceFinger(c *gin.Context) {
	dir := "resoure/reading"
	x, y, ok := resource_checkSN(c)
	if !ok {
		return
	}
	sx := GetRequestParam(c, "sx")
	sy := GetRequestParam(c, "sy")

	t := time.Now().Format("20060102/150405")
	id := t + utils.Uniqid() + "@" + strings.Join([]string{x, y, sx, sy}, ",")
	resource_uploadOss(c, dir, id)
}

// ResourceFingers 【弃用】
func ResourceFingers(c *gin.Context) {
	response.FailWithErrcode(response.E_PARAM, "---", c)
}

// ResourceFingersV3
// @Param sn string true "签名"
// @Param fnetx int false "坐标"
// @Param fnety int false "坐标"
// @Param snetx int false "坐标"
// @Param snety int false "坐标"
// @Param cnetx int false "坐标"
// @Param cnety int false "坐标"
// @Param knetx int false "坐标"
// @Param knety int false "坐标"
// @Param gnetx int true "坐标"
// @Param gnety int true "坐标"
// @Router /resource/fingers_v3 [post]
func ResourceFingersV3(c *gin.Context) {
	dir := "resoure/fingers_v3"
	sn := GetRequestParam(c, "sn")
	fnetx := GetRequestParam(c, "fnetx")
	fnety := GetRequestParam(c, "fnety")
	snetx := GetRequestParam(c, "snetx")
	snety := GetRequestParam(c, "snety")
	cnetx := GetRequestParam(c, "cnetx")
	cnety := GetRequestParam(c, "cnety")
	knetx := GetRequestParam(c, "knetx")
	knety := GetRequestParam(c, "knety")
	gnetx := GetRequestParam(c, "gnetx")
	gnety := GetRequestParam(c, "gnety")
	if sn == "" || gnetx == "" || gnety == "" {
		response.FailWithErrcode(response.E_PARAM, "参数错误", c)
		return
	}
	t := time.Now().Format("20060102/20060102150405")
	id := t + utils.Uniqid() + "@" + strings.Join([]string{
		fnetx, fnety, snetx, snety, cnetx, cnety,
		knetx, knety, gnetx, gnety,
	}, ",")
	resource_uploadOss(c, dir, id)
}

// ResourceFingers2
// @Param sn string true "签名"
// @Param x int true "x 坐标"
// @Param y int true "y 坐标"
// @Router /resource/fingers2 [post]
func ResourceFingers2(c *gin.Context) {
	dir := "resoure/fingers2"
	x, y, ok := resource_checkSN(c)
	if !ok {
		return
	}
	t := time.Now().Format("20060102/150405")
	id := t + utils.Uniqid() + "@" + x + "," + y
	resource_uploadOss(c, dir, id)
}

// ResourcePaper
// @Param sn string true "签名"
// @Param v string true "【未知】"
// @Router /resource/paper [post]
func ResourcePaper(c *gin.Context) {
	dir := "resoure/paper"
	sn := GetRequestParam(c, "sn")
	v := GetRequestParam(c, "v")
	if sn == "" || v == "" {
		response.FailWithErrcode(response.E_PARAM, "参数错误", c)
		return
	}
	t := time.Now().Format("20060102/150405")
	id := t + utils.Uniqid()
	resource_uploadOss(c, dir, id)
}

// ResourceLostFinger
// @Param sn string true "签名"
// @Param v string true "【未知】"
// @Router /resource/lostfinger [post]
func ResourceLostFinger(c *gin.Context) {
	dir := "resoure/lostfinger"
	sn := GetRequestParam(c, "sn")
	v := GetRequestParam(c, "v")
	serial := GetRequestParam(c, "serial")
	if sn == "" || v == "" {
		response.FailWithErrcode(response.E_PARAM, "参数错误", c)
		return
	}
	t := time.Now().Format("20060102/150405")
	id := serial + "/" + t + utils.Uniqid()
	resource_uploadOss(c, dir, id)
}

// ResourcePen
// @Param sn string true "签名"
// @Param x int true "x 坐标"
// @Param y int true "y 坐标"
// @Router /resource/pen [post]
func ResourcePen(c *gin.Context) {
	dir := "resoure/pen"
	x, y, ok := resource_checkSN(c)
	if !ok {
		return
	}
	// v := GetRequestParam(c, "v")
	// if v == "" {
	// 	response.FailWithErrcode(response.E_PARAM, "参数错误", c)
	// 	return
	// }
	t := time.Now().Format("20060102/150405")
	id := t + utils.Uniqid() + "@" + x + "," + y
	resource_uploadOss(c, dir, id)
}

// 上传 FormFile["image"] 到 oss dir/id + suffix
// @Succcess {json} json {"status": 200, "name": id + suffix}
func resource_uploadOss(c *gin.Context, dir, id string) {
	file, err := c.FormFile("image")
	if err != nil {
		response.FailWithErrcode(response.E_PARAM, "缺少image参数", c)
		return
	}
	if file.Size == 0 || file.Size >= 2097152 {
		response.FailWithErrcode(response.E_PARAM, "文件大小错误", c)
		return
	}
	mimetype := file.Header.Get("Content-Type")
	suffix := ""
	if mimetype == "image/png" {
		suffix = ".png"
	} else if mimetype == "image/jpeg" || mimetype == "image/pjpeg" {
		suffix = ".jpg"
	} else {
		response.FailWithErrcode(response.E_PARAM, "文件格式错误"+mimetype, c)
		return
	}
	fd, err := file.Open()
	if err != nil {
		response.FailWithErrcode(response.E_UNKNOWN, "临时文件打开失败", c)
		return
	}
	defer fd.Close()
	dst := dir + "/" + id + suffix
	if err := utils.UploadFile(fd, dst); err != nil {
		global.LOG.Debug("文件上传失败", zap.Error(err))
		response.FailWithErrcode(response.E_UNKNOWN, "上传失败", c)
	} else {
		response.OkWithData(gin.H{"status": 200, "name": id + suffix}, c)
	}
}
