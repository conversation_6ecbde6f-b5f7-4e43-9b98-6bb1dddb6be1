package service

import (
	"account/global"
	"account/model"
	"account/model/request"
	"account/model/response"
	"account/utils"
	"context"
	"errors"
	"math/rand"
	"strconv"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

// 使用中台账号注册或关联平板账号
func ZtRegister(c context.Context, req *request.ZtLoginReq) (data response.ZtLoginCheckResp, err error) {
	data, err = ZtCheck(c, &request.ZtCheckReq{ZtId: req.ZtId})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 已有账号关联的返回数据
	if data.Uid > 0 {
		return
	}
	// // 通过中台id获取家长手机号信息
	// parents, err := zt.GetStudentParents(&zt.GetStudentParentsReq{
	// 	StudentId: req.ZtId,
	// })
	// if err != nil {
	// 	return
	// }

	var (
		birth_y       int
		birth_m       time.Month
		birth_d       int
		pid, cid, did uint64
	)
	if req.Birthday != "" {
		if birthday, err1 := time.Parse(global.TIME_LAYOUT_DATE, req.Birthday); err1 == nil {
			birth_y, birth_m, birth_d = birthday.Date()
		}
	}
	pid, _ = strconv.ParseUint(strings.TrimRight(req.ProvinceId, "0"), 10, 64)
	cid, _ = strconv.ParseUint(strings.TrimRight(req.CityId, "0"), 10, 64)
	did, _ = strconv.ParseUint(strings.TrimRight(req.DistrictId, "0"), 10, 64)
	// 临时设置
	if pid == 0 {
		pid = 35
	}
	if cid == 0 {
		cid = 3505
	}
	if did == 0 {
		did = 350524
	}
	profile := model.UserProfile{
		Realname:   req.Nickname,
		Gender:     req.Gender,
		BirthY:     birth_y,
		BirthM:     int(birth_m),
		BirthD:     birth_d,
		ProvinceId: uint(pid),
		CityId:     uint(cid),
		DistrictId: uint(did),
		School:     req.School,
		Grade: func() int {
			if req.Grade == 0 {
				return 0
			}
			if req.Grade <= 9 {
				return 512 + req.Grade
			} else if req.Grade <= 12 {
				return 768 + (req.Grade - 10)
			} else if req.Grade >= 256 && req.Grade < 512 {
				req.Grade = 256
			} else if (req.Grade > 512 && req.Grade <= 521) || (req.Grade > 768 && req.Grade < 771) {
				return req.Grade
			}
			return 0
		}(),
	}
	var m *model.AcMember
	// for _, v := range parents.ParentList {
	// 	if v.Phone != "" {
	// 		if req.Mobile == "" {
	// 			req.Mobile = v.Phone
	// 		}
	// 		m, _ = GetMemberByUsername(v.Phone)
	// 		if m != nil && m.Uid > 0 {
	// 			req.Mobile = v.Phone
	// 			break
	// 		}
	// 	}
	// }
	if m == nil || m.Uid == 0 {
		// 手机号未注册的，生成账号，并绑定关联
		key := "account:ztRegister:" + req.ZtId
		ok, _ := global.Redis.SetNX(key, 1, time.Second*10).Result()
		if !ok {
			time.Sleep(100 * time.Millisecond)
			data, err = ZtCheck(c, &request.ZtCheckReq{ZtId: req.ZtId})
			if err != nil {
				err = response.New(response.E_FREQUENTLY, "登录频繁，请稍后重试", err)
			}
			return
		}
		defer global.Redis.Del(key)
		now := time.Now().Unix()
		timestr := strconv.FormatInt(now, 10)
		m = &model.AcMember{
			Username:     req.Mobile,
			Mobile:       req.Mobile,
			Regip:        req.Ip,
			Regdate:      now,
			Regapp:       req.Regapp,
			AccessExpire: now + global.TOKEN_EXPIRE,
		}
		if m.Username == "" {
			m.Username = uuid.NewV4().String()
		}
		if req.Password == "" {
			req.Password = strconv.Itoa(rand.Intn(********))
		}
		m.AccessToken = utils.MD5(m.Username + timestr)
		FormatMemberPassword(m, req.Password)

		err = global.DB.Transaction(func(tx *gorm.DB) error {
			ctx := global.InjectDB(c, tx)

			if err := CreateMemberCtx(ctx, m); err != nil {
				return err
			}
			profile.Uid = int(m.Uid)
			profile.Username = m.Username
			if err := CreateProfileCtx(ctx, &profile); err != nil {
				return err
			}
			if err = ZtBind(tx, req.ZtId, m.Uid); err != nil {
				return err
			}
			return nil
		})
		if err == nil {
			return response.ZtLoginCheckResp{
				AccessExpire: m.AccessExpire,
				AccessToken:  m.AccessToken,
				Avatar:       utils.GetAvatarUri(m.Uid),
				Mobile:       m.Mobile,
				Uid:          m.Uid,
				Username:     m.Username,
				IsRegister:   true,
			}, nil
		} else {
			return
		}
	}
	pm, err := ZtCheck(c, &request.ZtCheckReq{Uid: m.Uid})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 主账号未绑定，绑定账号，返回数据
	if pm.Uid == 0 {
		if err = ZtBind(global.DB, req.ZtId, m.Uid); err == nil {
			return response.ZtLoginCheckResp{
				AccessExpire: m.AccessExpire,
				AccessToken:  m.AccessToken,
				Avatar:       utils.GetAvatarUri(m.Uid),
				Mobile:       m.Mobile,
				Uid:          m.Uid,
				Username:     m.Username,
			}, nil
		} else {
			return
		}
	}
	m = nil
	err = global.DB.Transaction(func(tx *gorm.DB) error {
		var err error
		m, err = CreateSubmemberTx(tx, pm.Uid, req.Regapp, req.Ip)
		if err != nil {
			return err
		}
		if err = ZtBind(tx, req.ZtId, m.Uid); err == nil {
			return err
		}
		ctx := global.InjectDB(c, tx)
		profile.Uid = int(m.Uid)
		profile.Username = m.Username
		if err := CreateProfileCtx(ctx, &profile); err != nil {
			return err
		}
		return nil
	})
	if err != nil || m == nil {
		return
	}
	data = response.ZtLoginCheckResp{
		AccessExpire: m.AccessExpire,
		AccessToken:  m.AccessToken,
		Avatar:       utils.GetAvatarUri(m.Uid),
		Mobile:       m.Mobile,
		Uid:          m.Uid,
		Username:     m.Username,
		ZtID:         req.ZtId,
		IsRegister:   true,
	}
	return
}

func ZtCheck(c context.Context, req *request.ZtCheckReq) (data response.ZtLoginCheckResp, err error) {
	if req.Uid == 0 && req.ZtId == "" {
		err = response.New(response.E_PARAM, "需提供平板账号或中台账号", nil)
		return
	}
	db := global.DB.Table("ac_members m").
		Joins("Join zt_user zt on zt.uid = m.uid")
	if req.Uid > 0 {
		db = db.Where("zt.uid = ?", req.Uid)
	}
	if req.ZtId != "" {
		db = db.Where("zt.zt_id = ?", req.ZtId)
	}
	db.Select("m.access_expire, m.access_token, m.mobile, m.uid, m.username, zt.zt_id").Take(&data)
	if data.Uid == 0 {
		err = gorm.ErrRecordNotFound
		return
	}
	if strings.HasPrefix(data.Username, "sub-") {
		if puid, _ := GetPrimaryUid(data.Uid); puid > 0 {
			if pm, _ := GetMemberByUid(strconv.Itoa(int(puid))); pm != nil && pm.Mobile != "" {
				data.Mobile = pm.Mobile
			}
		}
	}
	data.Avatar = utils.GetAvatarUri(data.Uid)
	return
}

func ZtBind(tx *gorm.DB, ztId string, uid uint) (err error) {
	err = tx.Create(&model.ZtUser{
		Uid:       uid,
		ZtID:      ztId,
		CreatedAt: time.Now(),
	}).Error
	return
}
