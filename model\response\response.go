package response

import (
	"account/pkg/cryptor"
	"account/utils"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

// OkWithData 返回 json 数据
func OkWithData(data interface{}, c *gin.Context) {
	if encrypt, key := c.GetInt("encrypt"), c.Get<PERSON>("appsec"); encrypt == 1 && len(key) > 0 {
		if len(key) != 32 {
			key = utils.MD5(key)
		}
		buf, _ := json.Marshal(data)
		c.String(200, cryptor.AesCbcEncrypt(string(buf), key))
		return
	}
	c.JSON(http.StatusOK, data)
}

// Fail 直接返回错误码和错误信息
func Fail(errno int, errmsg string, c *gin.Context) {
	c.Set("error_code", errno)
	c.Set("uinfo", nil)
	c.Error(fmt.Errorf("%d: %s", errno, errmsg))
	OkWithData(gin.H{"errno": errno, "errmsg": errmsg}, c)
}

// FailWithErrcode 返回错误信息
// errcode 是系统定义的错误代码，errmsg 为空时使用 errcode 对应的默认错误信息
func FailWithErrcode(errcode int, errmsg string, c *gin.Context) {
	FailWithDetail(errcode, errmsg, gin.H{}, c)
}

// FailWithDetail 返回错误信息，并附加 data 数据
func FailWithDetail(errcode int, errmsg string, data gin.H, c *gin.Context) {
	msg := Errmsg(&errcode)
	if errmsg == "" {
		errmsg = msg
	}
	c.Set("error_code", errcode)
	c.Set("uinfo", nil)
	data["errno"] = errcode
	data["errmsg"] = errmsg
	c.Error(fmt.Errorf("%d: %s", errcode, errmsg))
	OkWithData(data, c)
}

func FailWithError(err error, c *gin.Context) {
	if err == nil {
		FailWithErrcode(E_UNKNOWN, "", c)
		return
	}
	c.Error(err)
	if ie, ok := err.(*IError); ok {
		FailWithErrcode(ie.Code, ie.Message, c)
	} else {
		FailWithErrcode(E_UNKNOWN, "", c)
	}
}
