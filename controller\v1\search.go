package v1

import (
	"account/model/response"
	"account/service"
	"account/utils"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SearchByUsernameOrRealname 通过账号或昵称搜索
// login yes
// @Param sn string true "签名"
// @Param key string true "账号/昵称"
// @Success {json} json {"result": [{"uid":1, "username": "", "realname": "", "avatar": "", "school": ""}]}
// @Router /search/urname [get]
func SearchByUrname(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if reason > 0 && !CheckMemberForbidden(c, m) {
		return
	}
	username := GetRequestParam(c, "key")
	if username == "" {
		response.FailWithErrcode(response.E_PARAM, "用户名不能为空", c)
		return
	}
	if len(username) > 32 {
		response.FailWithErrcode(response.E_PARAM, "用户名过长", c)
		return
	}
	username = strings.NewReplacer("%", "\\%", "_", "\\_").Replace(username)
	list, _ := service.SearchByUrname(username)
	data := make([]gin.H, 0, len(list))
	for _, val := range list {
		ret := gin.H{
			"uid":      strconv.Itoa(val.Uid),
			"username": val.Username,
			"realname": val.Realname,
			"avatar":   utils.GetAvatarUri(uint(val.Uid)),
			"school":   val.School,
		}
		data = append(data, ret)
	}
	response.OkWithData(gin.H{"result": data}, c)
}

// SearchByRealname 通过昵称搜索账号
// login yes
// @Param sn string true "签名"
// @Param key string true "昵称"
// @Success {json} json {"result": [{"uid":1, "username": "", "realname": "", "avatar": "", "school": ""}]}
// @Router /search/realname [get]
func SearchByRealname(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if reason > 0 && !CheckMemberForbidden(c, m) {
		return
	}
	realname := GetRequestParam(c, "key")
	if realname == "" {
		response.FailWithErrcode(response.E_PARAM, "昵称不能为空", c)
		return
	}
	if len(realname) > 32 {
		response.FailWithErrcode(response.E_PARAM, "昵称过长", c)
		return
	}
	realname = strings.NewReplacer("%", "\\%", "_", "\\_").Replace(realname)
	list, _ := service.SearchByRealname(realname)
	data := make([]gin.H, 0, len(list))
	for _, val := range list {
		ret := gin.H{
			"uid":      strconv.Itoa(val.Uid),
			"username": val.Username,
			"realname": val.Realname,
			"avatar":   utils.GetAvatarUri(uint(val.Uid)),
			"school":   val.School,
		}
		data = append(data, ret)
	}
	response.OkWithData(gin.H{"result": data}, c)
}

// SearchByMobile 通过手机搜索账号
// login yes
// @Param sn string true "签名"
// @Param key string true "手机号"
// @Success {json} json {"result": [{"uid":1, "username": "", "realname": "", "avatar": "", "school": ""}]}
// @Router /search/mobile [get]
func SearchByMobile(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if reason > 0 && !CheckMemberForbidden(c, m) {
		return
	}
	mobile := GetRequestParam(c, "key")
	if mobile == "" {
		response.FailWithErrcode(response.E_PARAM, "手机号不能为空", c)
		return
	}
	if !utils.TestPhone(mobile) {
		response.FailWithErrcode(response.E_MOBILE_FMT, "", c)
		return
	}
	app := GetRequestParam(c, "appid")
	data := []gin.H{}
	var err error
	if app != "" && app != "com.readboy.personalsetting" {
		m, err = service.GetMemberByMobileAppid(c, mobile, app)
	} else {
		m, err = service.GetMemberByMobile(mobile)
	}
	if err == nil {
		ret := gin.H{
			"uid":      strconv.Itoa(int(m.Uid)),
			"username": m.Username,
			"avatar":   utils.GetAvatarUri(m.Uid),
		}
		if p, err := service.GetProfileByUid(m.Uid); err == nil {
			ret["username"] = p.Username
			ret["realname"] = p.Realname
			ret["school"] = p.School
		}
		if sublist, err := service.GetSubmembersByUid(m.Uid); err == nil {
			subids := make([]uint, 0, len(sublist))
			for _, v := range sublist {
				subids = append(subids, v.Suid)
			}
			ret["subuids"] = subids
		}
		data = append(data, ret)
	}
	response.OkWithData(gin.H{"result": data}, c)
}

// SearchByEmail 通过邮箱搜索账号
// login yes
// @Param sn string true "签名"
// @Param key string true "邮箱"
// @Success {json} json {"result": [{"uid":1, "username": "", "realname": "", "avatar": "", "school": ""}]}
// @Router /search/email
func SearchByEmail(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if reason > 0 && !CheckMemberForbidden(c, m) {
		return
	}
	email := GetRequestParam(c, "key")
	if email == "" {
		response.FailWithErrcode(response.E_PARAM, "邮箱号不能为空", c)
		return
	}
	if !utils.TestEmail(email) {
		response.FailWithErrcode(response.E_EMAIL_FMT, "", c)
		return
	}
	data := []gin.H{}
	if m, err := service.GetMemberByEmail(email); err == nil {
		if p, err := service.GetProfileByUid(m.Uid); err == nil {
			ret := gin.H{
				"uid":      strconv.Itoa(p.Uid),
				"username": p.Username,
				"realname": p.Realname,
				"avatar":   utils.GetAvatarUri(uint(p.Uid)),
				"school":   p.School,
			}
			data = append(data, ret)
		}
	}
	response.OkWithData(gin.H{"result": data}, c)
}
