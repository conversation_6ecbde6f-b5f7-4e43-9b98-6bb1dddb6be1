package middleware

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(logFormatter)
}

var print_fmt = `[GIN]%v |%s %3d %s| %10v | %15s |%s %-5s %s %#v - "%s"` + "\n%s"

func logFormatter(param gin.LogFormatterParams) string {
	var statusColor, methodColor, resetColor string
	if param.IsOutputColor() {
		statusColor = param.StatusCodeColor()
		methodColor = param.MethodColor()
		resetColor = param.ResetColor()
	}

	if param.Latency > time.Minute {
		// Truncate in a golang < 1.8 safe way
		param.Latency = param.Latency - param.Latency%time.Second
	}
	// [GIN]2021/03/25 - 08:00:00 | 200 |         0s | *************** | GET   "/member/count?sn=" - "PostmanRuntime/7.26.8"
	// Error #1: ...
	// Error #2: ...
	return fmt.Sprintf(print_fmt,
		param.TimeStamp.Format("2006/01/02 - 15:04:05"),
		statusColor, param.StatusCode, resetColor,
		param.Latency,
		param.ClientIP,
		methodColor, param.Method, resetColor,
		param.Path,
		param.Request.UserAgent(),
		param.ErrorMessage,
	)

}
