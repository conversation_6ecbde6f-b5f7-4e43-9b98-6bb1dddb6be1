package config

// 网络身份认证配置
type CtdidConfig struct {

	// 测试环境接口地址
	TestUrls []string `mapstructure:"test_urls" json:"test_urls" yaml:"test_urls"`

	// 生产环境接口地址
	ProdUrls []string `mapstructure:"prod_urls" json:"prod_urls" yaml:"prod_urls"`

	// 请求超时时间（秒）
	Timeout int `mapstructure:"timeout" json:"timeout" yaml:"timeout"`

	// 重试次数
	RetryCount int `mapstructure:"retry_count" json:"retry_count" yaml:"retry_count"`
}
