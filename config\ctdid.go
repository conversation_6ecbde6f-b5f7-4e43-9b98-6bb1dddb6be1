package config

// 网络身份认证配置
type CtdidConfig struct {
	// 是否使用测试环境
	UseTestEnv bool `mapstructure:"use_test_env" json:"use_test_env" yaml:"use_test_env"`

	// 测试环境接口地址
	TestUrls []string `mapstructure:"test_urls" json:"test_urls" yaml:"test_urls"`

	// 生产环境接口地址
	ProdUrls []string `mapstructure:"prod_urls" json:"prod_urls" yaml:"prod_urls"`

	// 请求超时时间（秒）
	Timeout int `mapstructure:"timeout" json:"timeout" yaml:"timeout"`

	// 是否跳过SSL证书验证
	SkipSSLVerify bool `mapstructure:"skip_ssl_verify" json:"skip_ssl_verify" yaml:"skip_ssl_verify"`

	// 重试次数
	RetryCount int `mapstructure:"retry_count" json:"retry_count" yaml:"retry_count"`

	// 是否启用模拟模式（不请求真实接口）
	MockMode bool `mapstructure:"mock_mode" json:"mock_mode" yaml:"mock_mode"`
}

// 默认配置
func DefaultCtdidConfig() CtdidConfig {
	return CtdidConfig{
		UseTestEnv: true, // 默认使用测试环境
		TestUrls: []string{
			"https://*************:20002/uentrance/interf/auth/request",  // 中国电信
			"https://***********:20002/uentrance/interf/auth/request",    // 中国移动
			"https://*************:20002/uentrance/interf/auth/request",  // 中国联通
		},
		ProdUrls: []string{
			"https://*************:6444/uentrance/interf/auth/request",   // 中国电信
			"https://***********:6444/uentrance/interf/auth/request",     // 中国移动
			"https://*************:6444/uentrance/interf/auth/request",   // 中国联通
		},
		Timeout:       30,    // 30秒超时
		SkipSSLVerify: true,  // 跳过SSL验证（测试环境）
		RetryCount:    3,     // 重试3次
		MockMode:      false, // 默认不使用模拟模式
	}
}

// 初始化配置，确保有默认值
func (c *CtdidConfig) InitDefaults() {
	if len(c.TestUrls) == 0 {
		c.TestUrls = []string{
			"https://*************:20002/uentrance/interf/auth/request",
			"https://***********:20002/uentrance/interf/auth/request",
			"https://*************:20002/uentrance/interf/auth/request",
		}
	}

	if len(c.ProdUrls) == 0 {
		c.ProdUrls = []string{
			"https://*************:6444/uentrance/interf/auth/request",
			"https://***********:6444/uentrance/interf/auth/request",
			"https://*************:6444/uentrance/interf/auth/request",
		}
	}

	if c.Timeout <= 0 {
		c.Timeout = 30
	}

	if c.RetryCount <= 0 {
		c.RetryCount = 3
	}
}
