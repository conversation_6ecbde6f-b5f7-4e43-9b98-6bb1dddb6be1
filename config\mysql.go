package config

type Mysql struct {
	Path            string `mapstructure:"path" json:"path" yaml:"path"`
	Config          string `mapstructure:"config" json:"config" yaml:"config"`
	Dbname          string `mapstructure:"db-name" json:"dbname" yaml:"db-name"`
	Username        string `mapstructure:"username" json:"username" yaml:"username"`
	Password        string `mapstructure:"password" json:"password" yaml:"password"`
	MaxIdleConns    int    `mapstructure:"max-idle-conns" json:"maxIdleConns" yaml:"max-idle-conns"`
	MaxOpenConns    int    `mapstructure:"max-open-conns" json:"maxOpenConns" yaml:"max-open-conns"`
	MaxConnLifetime int    `mapstructure:"max-conn-lifetime" json:"maxConnLifetime" yaml:"max-conn-lifetime"`
	LogMode         bool   `mapstructure:"log-mode" json:"logMode" yaml:"log-mode"`
	LogZap          string `mapstructure:"log-zap" json:"logZap" yaml:"log-zap"`
}

func (m *Mysql) Dsn() string {
	return m.Username + ":" + m.Password + "@tcp(" + m.Path + ")/" + m.Dbname + "?" + m.Config
}
