package service

import (
	"account/global"
	"account/model"
	"account/model/request"
	"account/utils"
	"bytes"
	"crypto/tls"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetCtdidMember 网络身份认证时用，参考GetWxMember，通过PID查找用户
func GetCtdidMember(pid, mobile string) (m *model.AcMember, err error) {
	var mem model.AcMember
	// 优先通过网络身份认证PID查找
	if pid != "" {
		err = global.DB.Model(&mem).First(&mem, "ctdid_pid=?", pid).Error
		if err == nil {
			return &mem, nil
		}
	}
	// 如果没找到，尝试通过手机号查找
	if mobile != "" {
		err = global.DB.Model(&mem).First(&mem, "mobile=?", mobile).Error
	}
	return &mem, err
}

// 国标库响应结构
type NationalAuthResponse struct {
	Sign       string `json:"sign"`
	BizPackage struct {
		BizSerialNo string `json:"bizSerialNo"`
		ResultCode  string `json:"resultCode"`
		ResultDesc  string `json:"resultDesc"`
		BizSeq      string `json:"bizSeq"`
		Data        *struct {
			PID               string `json:"PID"`
			PhotoCompareScore string `json:"photoCompareScore,omitempty"`
			EncryptedIdInfo   string `json:"encryptedIdInfo,omitempty"`
		} `json:"data,omitempty"`
	} `json:"bizPackage"`
}

// requestNationalAuth 请求国标库进行身份认证，获取PID
func requestNationalAuth(req *request.CtdidAuthBizPackage) (pid string, mobile string, realName string, err error) {

	// 构建请求到国标库的数据
	requestData := map[string]interface{}{
		"bizPackage": req,
	}

	// 转换为JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return "", "", "", fmt.Errorf("构建请求数据失败: %v", err)
	}

	fmt.Printf("发送到国标库的数据: %s\n", string(jsonData))

	// 从配置中选择接口地址
	var urls []string
	if global.CONFIG.App.Env != "public" {
		urls = global.CONFIG.Ctdid.TestUrls
		fmt.Printf("使用测试环境接口\n")
	} else {
		urls = global.CONFIG.Ctdid.ProdUrls
		fmt.Printf("使用生产环境接口\n")
	}

	// 尝试请求各个接口（容错机制）
	for i, url := range urls {
		fmt.Printf("尝试请求接口 %d: %s\n", i+1, url)

		response, requestErr := callNationalAPI(url, jsonData)
		if requestErr != nil {
			fmt.Printf("接口 %d 请求失败: %v\n", i+1, requestErr)
			continue
		}

		// 检查响应结果
		if response.BizPackage.ResultCode == "C0000000" {
			// 成功
			pid = response.BizPackage.Data.PID
			fmt.Printf("国标库认证成功 - PID: %s\n", pid)

			// 解析 PID 获取网络身份应用标识
			networkID, err := extractNetworkIDFromPID(pid)
			if err != nil {
				fmt.Printf("警告：解析网络身份应用标识失败: %v\n", err)
			} else {
				fmt.Printf("网络身份应用标识: %s\n", networkID)
			}

			fmt.Printf("========================\n")
			return pid, mobile, realName, nil
		} else {
			// 业务失败
			fmt.Printf("国标库业务失败 - Code: %s, Desc: %s\n",
				response.BizPackage.ResultCode, response.BizPackage.ResultDesc)
		}
	}

	// 所有接口都失败，返回模拟数据
	fmt.Printf("所有国标库接口都失败\n")

	return pid, mobile, realName, nil
}

// callNationalAPI 调用国标库接口
func callNationalAPI(url string, jsonData []byte) (*NationalAuthResponse, error) {
	// 从配置中获取设置
	config := global.CONFIG.Ctdid

	// 创建TLS配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true, // 暂时跳过SSL验证
	}

	// 固定证书路径：当前目录下的读书郎教育科技有限公司文件夹
	certDir := "读书郎教育科技有限公司"
	certFile := filepath.Join(certDir, "cert.cer")      // 实际文件名是 cert.cer
	keyFile := filepath.Join(certDir, "prikey.pem")     // 实际文件名是 prikey.pem
	caFile := filepath.Join(certDir, "downstream_root.cer")

	// 加载客户端证书和私钥
	cert, err := tls.LoadX509KeyPair(certFile, keyFile)
	if err != nil {
		fmt.Printf("警告：加载客户端证书失败: %v\n", err)
		fmt.Printf("将使用不带证书的连接\n")
	} else {
		tlsConfig.Certificates = []tls.Certificate{cert}
		fmt.Printf("已加载客户端证书: %s\n", certFile)

		// 加载CA根证书
		caCert, err := os.ReadFile(caFile)
		if err != nil {
			fmt.Printf("警告：读取CA证书失败: %v\n", err)
		} else {
			caCertPool := x509.NewCertPool()
			if !caCertPool.AppendCertsFromPEM(caCert) {
				fmt.Printf("警告：解析CA证书失败\n")
			} else {
				tlsConfig.RootCAs = caCertPool
				tlsConfig.InsecureSkipVerify = false // 有CA证书时启用验证
				fmt.Printf("已加载CA根证书: %s\n", caFile)
			}
		}
	}

	// 创建HTTPS客户端
	tr := &http.Transport{
		TLSClientConfig: tlsConfig,
		// 添加连接池配置
		MaxIdleConns:        10,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     30 * time.Second,
	}
	client := &http.Client{
		Transport: tr,
		Timeout:   time.Duration(config.Timeout) * time.Second,
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头（根据您提供的 Go 示例使用 application/octet-stream）
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("User-Agent", "CTDID-Client/1.0")

	fmt.Printf("发送请求头: Content-Type=%s\n", req.Header.Get("Content-Type"))
	fmt.Printf("请求体长度: %d 字节\n", len(jsonData))

	// 打印请求体前100字符
	previewLen := 100
	if len(jsonData) < previewLen {
		previewLen = len(jsonData)
	}
	fmt.Printf("请求体前%d字符: %s\n", previewLen, string(jsonData[:previewLen]))

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	fmt.Printf("国标库响应状态: %d\n", resp.StatusCode)
	fmt.Printf("国标库响应内容: %s\n", string(body))

	// 尝试解析响应中的 data 字段
	var tempResp map[string]interface{}
	if json.Unmarshal(body, &tempResp) == nil {
		if dataStr, ok := tempResp["data"].(string); ok && dataStr != "" {
			fmt.Printf("=== 解码 data 字段 ===\n")
			if decodedData, err := base64.StdEncoding.DecodeString(dataStr); err == nil {
				fmt.Printf("Base64解码成功，数据长度: %d 字节\n", len(decodedData))

				// 以十六进制格式显示前100字节（避免输出过长）
				displayLen := 100
				if len(decodedData) < displayLen {
					displayLen = len(decodedData)
				}
				fmt.Printf("数据前%d字节(十六进制): %x\n", displayLen, decodedData[:displayLen])

				// 检查数据格式
				if len(decodedData) > 0 {
					fmt.Printf("首字节: 0x%02x", decodedData[0])
					if decodedData[0] == 0x30 {
						fmt.Printf(" (ASN.1 SEQUENCE - 这是正常的加密数据格式)")
					}
					fmt.Println()
				}
				fmt.Println("====================")

				// 打印前100字节的十六进制
				hexLen := 100
				if len(decodedData) < hexLen {
					hexLen = len(decodedData)
				}
				fmt.Printf("数据前%d字节(hex): %x\n", hexLen, decodedData[:hexLen])

				// 检查是否是可打印文本
				if len(decodedData) > 0 && decodedData[0] >= 32 && decodedData[0] <= 126 {
					textLen := 200
					if len(decodedData) < textLen {
						textLen = len(decodedData)
					}
					fmt.Printf("可能的文本内容: %s\n", string(decodedData[:textLen]))
				}
			} else {
				fmt.Printf("Base64解码失败: %v\n", err)
			}
		}
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("HTTP错误: %d - %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response NationalAuthResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &response, nil
}

// extractNetworkIDFromPID 从 PID 中提取网络身份应用标识
// 根据国标规范：Base64解码后，第1-40字节为网络身份应用标识
func extractNetworkIDFromPID(pidBase64 string) (string, error) {
	// Base64 解码
	decodedData, err := base64.StdEncoding.DecodeString(pidBase64)
	if err != nil {
		return "", fmt.Errorf("Base64解码失败: %v", err)
	}

	// 检查数据长度（至少需要41字节：版本号1字节 + 网络身份应用标识40字节）
	if len(decodedData) < 41 {
		return "", fmt.Errorf("PID数据长度不足，期望至少41字节，实际%d字节", len(decodedData))
	}

	// 提取网络身份应用标识（第1-40字节，索引1-40）
	networkIDBytes := decodedData[1:41]

	// 转换为十六进制字符串（这是标准的表示方式）
	networkID := fmt.Sprintf("%x", networkIDBytes)

	fmt.Printf("=== PID 解析详情 ===\n")
	fmt.Printf("PID总长度: %d 字节\n", len(decodedData))
	fmt.Printf("版本号: 0x%02x (%d)\n", decodedData[0], decodedData[0])
	fmt.Printf("网络身份应用标识(hex): %s\n", networkID)
	fmt.Printf("网络身份应用标识长度: %d 字节\n", len(networkIDBytes))

	// 如果需要，也可以尝试解析时间戳（第41-54字节）
	if len(decodedData) >= 55 {
		timestampBytes := decodedData[41:55]
		timestamp := string(timestampBytes)
		fmt.Printf("时间戳: %s\n", timestamp)
	}

	return networkID, nil
}

// decryptDataPackage 尝试解密数据包获取真正的 PID
// 注意：这个函数目前只是示例，实际的解密需要根据具体的加密算法和密钥
func decryptDataPackage(encryptedData []byte) (string, error) {
	// 检查是否是 ASN.1/DER 格式的加密数据包
	if len(encryptedData) == 0 || encryptedData[0] != 0x30 {
		return "", fmt.Errorf("不是有效的 ASN.1 数据包")
	}

	fmt.Printf("检测到 ASN.1/DER 格式的加密数据包\n")
	fmt.Printf("数据包可能包含：\n")
	fmt.Printf("- 加密的 PID 数据\n")
	fmt.Printf("- 数字签名\n")
	fmt.Printf("- 证书信息\n")

	// TODO: 这里需要实现实际的解密逻辑
	// 可能需要：
	// 1. 使用客户端私钥解密
	// 2. 验证数字签名
	// 3. 提取内部的 PID 数据

	// 目前返回错误，提示需要实现解密
	return "", fmt.Errorf("需要实现具体的解密算法，可能需要使用客户端私钥")
}

// ProcessCtdidAuth 处理网络身份认证请求，参考WxRegister实现
func ProcessCtdidAuth(req *request.CtdidAuthBizPackage, ip string, appid string) (gin.H, error) {
	// 第一步：请求国标库进行身份认证，获取PID和用户信息
	pid, mobile, realName, err := requestNationalAuth(req)
	fmt.Println(pid, mobile, realName)
	if err != nil {
		return nil, fmt.Errorf("国标库认证失败: %v", err)
	}
	return nil, nil

	now := time.Now().Unix()
	isRegister := false // 是否为注册

	// 第二步：使用PID查找现有用户
	m, err := GetCtdidMember(pid, mobile)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果没有手机号，只验证不落库
		if mobile == "" {
			return processAuthWithoutUser(req, pid), nil
		}

		// 创建新用户
		m = &model.AcMember{}
		m.Regip = ip
		m.Regdate = now
		m.Regapp = appid
		m.AccessToken = utils.MD5(mobile + strconv.FormatInt(now, 10))
		isRegister = true
	}

	// 设置用户信息
	m.Username = "ctdid-" + utils.MD5(pid)[:8] + strconv.FormatInt(now, 10)[:6]
	m.AccessExpire = now + global.TOKEN_EXPIRE
	m.Forbidden = 0
	m.Mobile = mobile
	m.CtdidPid = pid // 存储网络身份认证PID

	if isRegister {
		if err = CreateMember(m); err != nil {
			return nil, err
		}
	} else {
		if err = SaveMember(m); err != nil {
			return nil, err
		}
	}

	// 生成成功响应，参考微信登录的返回格式
	data := gin.H{
		"uid":           strconv.Itoa(int(m.Uid)),
		"username":      m.Username,
		"mobile":        m.Mobile,
		"forbidden":     m.Forbidden,
		"access_token":  m.AccessToken,
		"access_expire": m.AccessExpire,
		"bizSeq":        req.BizSeq,
		"mode":          req.Mode,
		"pid":           pid,      // 返回国标库的PID
		"realName":      realName, // 返回真实姓名
	}

	// 添加人脸相似度分值（如果需要）
	if req.RequiresPhoto() {
		data["photoCompareScore"] = "968.78"
	}

	// 添加年龄标识（如果需要）
	if req.RequiresAgeInfo() {
		data["ageGroup"] = "09"
		data["encryptedIdInfo"] = encryptAgeInfo("09")
	}

	return data, nil
}

// processAuthWithoutUser 处理不创建用户的认证
func processAuthWithoutUser(req *request.CtdidAuthBizPackage, pid string) gin.H {
	// 构建响应数据
	data := gin.H{
		"bizSeq":   req.BizSeq,
		"mode":     req.Mode,
		"pid":      pid, // 使用国标库返回的PID
		"verified": true,
		"message":  "身份验证成功，未创建用户记录",
	}

	// 添加人脸相似度分值（如果需要）
	if req.RequiresPhoto() {
		data["photoCompareScore"] = "968.78"
	}

	// 添加年龄标识（如果需要）
	if req.RequiresAgeInfo() {
		data["ageGroup"] = "09"
		data["encryptedIdInfo"] = encryptAgeInfo("09")
	}

	return data
}

// encryptAgeInfo 加密年龄标识
func encryptAgeInfo(ageGroup string) string {
	// 模拟加密年龄标识
	ageInfo := fmt.Sprintf(`{"NL":"%s"}`, ageGroup)
	return base64.StdEncoding.EncodeToString([]byte(ageInfo))
}


