package ws

import (
	"sync"

	"github.com/gorilla/websocket"
)

type scanLoginManager struct {
	clients    map[string]*Client
	lock       sync.RWMutex
	register   chan *Client
	unRegister chan *Client
}

type Client struct {
	ID     string
	Socket *websocket.Conn
	Send   chan interface{}
}

var ScanLoginManager = &scanLoginManager{
	clients:    make(map[string]*Client),
	lock:       sync.RWMutex{},
	register:   make(chan *Client),
	unRegister: make(chan *Client),
}

// 注册
func (manager *scanLoginManager) Register(client *Client) {
	manager.register <- client
}

// 注销
func (manager *scanLoginManager) UnRegister(client *Client) {
	manager.unRegister <- client
}

func (manager *scanLoginManager) Start() {
	for {
		select {
		case conn := <-manager.register:
			manager.lock.Lock()
			manager.clients[conn.ID] = conn
			manager.lock.Unlock()
		case conn := <-manager.unRegister:
			manager.lock.Lock()
			close(conn.Send)
			delete(manager.clients, conn.ID)
			manager.lock.Unlock()
		}
	}
}

func (manager *scanLoginManager) Send(id string, msg interface{}) (exists bool) {
	manager.lock.RLock()
	defer manager.lock.RUnlock()
	if conn := manager.clients[id]; conn != nil {
		exists = true
		conn.Send <- msg
	}
	return
}

func (client *Client) NothingRead() {
	defer func() {
		ScanLoginManager.UnRegister(client)
		client.Socket.Close()
	}()
	for {
		mt, _, err := client.Socket.ReadMessage()
		if err != nil {
			return
		}
		if mt == websocket.CloseMessage {
			return
		}
	}
}
