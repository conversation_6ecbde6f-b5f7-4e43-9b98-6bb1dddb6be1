package router

import (
	v1 "account/controller/v1"

	"github.com/gin-gonic/gin"
)

func InitMobileRouter(Router *gin.RouterGroup) {
	ApiRouter := Router.Group("mobile")
	{
		Any(ApiRouter, "login", v1.MobileLogin)
		Any(ApiRouter, "register", v1.MobileRegister)
		Any(ApiRouter, "unregister", v1.MobileUnregister)
		Any(ApiRouter, "verify", v1.MobileVerify)
		Any(ApiRouter, "bind", v1.MobileBind)
		Any(ApiRouter, "rebind", v1.MobileRebind)
		Any(ApiRouter, "resetpwd", v1.MobileResetPassword)
		Any(ApiRouter, "verify2", v1.MobileVerify2)
		Any(ApiRouter, "login2", v1.MobileLogin2)
	}
}
