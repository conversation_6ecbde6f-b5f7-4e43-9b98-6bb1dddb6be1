package service

import (
	"account/global"
	"account/model"
	"account/utils"
	"context"
	"errors"
	"strconv"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func CreateSubmember(m *model.AcSubmember) error {
	return global.DB.Model(m).Create(m).Error
}

// 获取 readboy 子账号
func GetSubmembersByUid(uid uint) (list []model.AcSubmember, err error) {
	err = global.DB.Model(&model.AcSubmember{}).Where("uid = ? And suid > 0", uid).Order("sort").Find(&list).Error
	return
}

func GetSubmembersBySuid(uid, suid uint) (m model.AcSubmember, err error) {
	err = global.DB.Model(&model.AcSubmember{}).Where("uid = ? And suid = ?", uid, suid).Take(&m).Error
	return
}

func DeleteSubmemberByID(id int64) (err error) {
	err = global.DB.Model(&model.AcSubmember{}).Where("id = ?", id).Delete(&model.AcSubmember{}).Error
	return
}

func SubmemberReorder(uid uint, uids []int64) (err error) {
	if len(uids) == 0 {
		return
	}
	sql := "Update ac_submembers Set `sort` = case"
	values := make([]interface{}, 0, 1+2*len(uids))
	for i, id := range uids {
		sql += " when suid = ? then ?"
		values = append(values, id, i+1)
	}
	sql += " end Where uid = ?"
	values = append(values, uid)
	return global.DB.Exec(sql, values...).Error
}

func GetSubmembersByUidWithTx(tx *gorm.DB, uid uint) (list []model.AcSubmember, err error) {
	err = tx.Raw("Select * From ac_submembers Where uid = ? For Update", uid).Find(&list).Error
	return
}

func CreateSubmemberWithTx(tx *gorm.DB, m *model.AcSubmember) error {
	return tx.Model(m).Create(m).Error
}

func UpdateSubmemberWithTx(tx *gorm.DB, id int64, value map[string]interface{}) error {
	return tx.Model(&model.AcSubmember{}).Where("id = ?", id).Updates(value).Error
}

func GetPrimaryUid(suid uint) (uid uint, err error) {
	var m model.AcSubmember
	err = global.DB.Model(&m).Where("suid = ?", suid).Select("uid").First(&m).Error
	uid = m.Uid
	return
}

// 重置子账号token
func ResetSubmemberAccesstoken(uid uint) {
	suids := make([]int, 0, 3)
	global.DB.Model(&model.AcSubmember{}).Where("uid = ? And suid <> ?", uid, uid).Pluck("suid", &suids)
	if len(suids) > 0 {
		timestr := strconv.FormatInt(time.Now().Unix(), 10)
		global.DB.Exec("Update ac_members Set access_token = md5(concat(username, ?)) Where uid in (?)", timestr, suids)
		DelRedisMember(suids...)
	}
}

func CreatePlatformSubmember(c context.Context, m *model.PlatformSubmember) error {
	return global.GetDB(c).Model(m).Clauses(clause.OnConflict{DoNothing: true}).Create(m).Error
}

// 获取指定平台子账号
func GetPlatformSubmembersByUid(c context.Context, uid uint, platform string) (list []model.PlatformSubmember, err error) {
	err = global.GetDB(c).Model(&list).Where("uid = ? And suid > 0", uid, platform).
		Order("sort").Find(&list).Error
	return
}

// ExistsSubmember 确认 uid 是否关联子账号 suid
func ExistsSubmember(c context.Context, uid, suid uint) (exists bool, err error) {
	var m model.AcSubmember
	err = global.GetDB(c).Model(&m).
		Where("uid = ? And suid = ?", uid, suid).Take(&m).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if m.ID == 0 {
		var pm model.PlatformSubmember
		err = global.GetDB(c).Model(&pm).Where("uid = ? And suid = ?", uid, suid).Take(&pm).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		return pm.Uid > 0, nil
	}
	return m.ID > 0, nil
}

func CreateSubmemberTx(tx *gorm.DB, puid uint, appid string, ip string) (m *model.AcMember, err error) {
	now := time.Now().Unix()
	m = &model.AcMember{
		Regip:     ip,
		Regdate:   now,
		Regapp:    appid,
		Forbidden: 0,
	}
	slist, err := GetSubmembersByUidWithTx(tx, puid)
	if err != nil {
		return
	}
	if len(slist) >= 4 {
		err = errors.New("子账号数量达到上限")
		return
	}
	// 创建子账号映射
	subm := model.AcSubmember{
		Uid:  puid,
		Sort: len(slist) + 1,
	}
	if err = CreateSubmemberWithTx(tx, &subm); err != nil {
		return
	}

	// 创建子账号
	m.Username = "sub-" + strconv.Itoa(int(subm.ID))
	m.AccessToken = utils.MD5(m.Username + strconv.FormatInt(now, 10))
	m.AccessExpire = now + int64(global.TOKEN_EXPIRE)
	if err = CreateMember(m); err != nil {
		return
	}
	// 回填子账号 uid
	if err = UpdateSubmemberWithTx(tx, subm.ID, map[string]interface{}{"suid": m.Uid}); err != nil {
		return
	}
	return
}
