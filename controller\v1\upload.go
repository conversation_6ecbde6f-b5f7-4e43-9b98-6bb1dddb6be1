package v1

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/service"
	"account/utils"
	"bytes"
	"fmt"
	"io/ioutil"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// UploadAvatar 上传用户头像文件
// login yes
// @Param sn string true "签名"
// @Param avatar | image file true "图片文件"
// @Success {json} json {"status": 200, "avatar": ""}
// @Router /upload/avatar [post]
func UploadAvatar(c *gin.Context) {
	m, ok := CheckLogin(c)
	if !ok {
		return
	}
	fmt.Println(m.Uid)
	if ok := service.CheckProfileLimitConfig(model.ProfileLimitConfigTypeAvatar, c.ClientIP()); ok {
		response.FailWithErrcode(response.E_SEVER_CLOSED, "由于系统维护，请稍后再试", c)
		return
	}

	avatar, err := c.FormFile("avatar")
	if err != nil {
		avatar, err = c.FormFile("image")
	}
	if err != nil {
		response.FailWithErrcode(response.E_PARAM, err.Error(), c)
		return
	}
	if avatar.Size > 2097152 || avatar.Size == 0 {
		response.FailWithErrcode(response.E_PARAM, "文件大小错误", c)
		return
	}
	mimetype := avatar.Header.Get("Content-Type")
	if mimetype != "image/png" && mimetype != "image/jpeg" && mimetype != "image/pjpeg" {
		response.FailWithErrcode(response.E_PARAM, "文件格式错误"+mimetype, c)
		return
	}
	dst := global.CONFIG.App.AvatarDir + "/" + strconv.Itoa(int(m.Uid))
	fd, err := avatar.Open()
	if err != nil {
		response.FailWithErrcode(response.E_UNKNOWN, "临时文件打开失败", c)
		return
	}
	imgbuf, _ := ioutil.ReadAll(fd)
	fd.Close()
	pass, err := service.ShumeiImgCheck(m.Uid, imgbuf)
	if err != nil || !pass {
		response.FailWithErrcode(response.E_UNKNOWN, "头像审核失败", c)
		return
	}

	rd := bytes.NewBuffer(imgbuf)
	if err := utils.UploadFile(rd, dst); err != nil {
		global.LOG.Debug("头像上传失败", zap.Uint("uid", m.Uid), zap.Error(err))
		response.FailWithErrcode(response.E_UNKNOWN, "文件上传失败", c)
	} else {
		response.OkWithData(gin.H{"status": 200, "avatar": utils.GetAvatarUri(m.Uid)}, c)
	}
}

// UploadBase64Avatar 上传用户头像文件(base64编码)
// login yes
// @Param sn string true "签名"
// @Param avatar | image file true "图片文件"
// @Success {json} json {"status": 200, "avatar": ""}
// @Router /upload/avatar2 [post]
func UploadBase64Avatar(c *gin.Context) {
	m, ok := CheckLogin(c)
	if !ok {
		return
	}
	avatar, ok := c.GetPostForm("avatar")
	if !ok {
		avatar, ok = c.GetPostForm("image")
	}
	if !ok {
		response.FailWithErrcode(response.E_PARAM, "未指定图片", c)
		return
	}
	if len(avatar) == 0 || len(avatar) > 2097152 {
		response.FailWithErrcode(response.E_PARAM, "文件大小错误", c)
		return
	}
	dcode, err := utils.Base64Decode(avatar)
	if err != nil {
		response.FailWithErrcode(response.E_UNKNOWN, "编码错误", c)
		return
	}
	r := bytes.NewReader(dcode)
	dst := global.CONFIG.App.AvatarDir + "/" + strconv.Itoa(int(m.Uid))
	if err := utils.UploadFile(r, dst); err != nil {
		global.LOG.Debug("头像上传失败", zap.Uint("uid", m.Uid), zap.Error(err))
		response.FailWithErrcode(response.E_UNKNOWN, "文件上传失败", c)
	} else {
		response.OkWithData(gin.H{"status": 200, "avatar": utils.GetAvatarUri(m.Uid)}, c)
	}
}
