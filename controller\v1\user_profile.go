package v1

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/service"
	"account/utils"
	"encoding/json"
	"errors"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

// ProfileGet 获取用户个人资料
// login yes
// @Param sn string true "签名"
// @Success 200 {json} json {"uid": 1, "username": "admin", "mobile": "", "email": "", "avatar": ""}
// @Router /profile/get [get]
func ProfileGet(c *gin.Context) {
	m, loginOK := CheckLogin(c)
	if !loginOK {
		return
	}
	p, err := service.GetProfileByUid(m.Uid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			p.Uid = int(m.Uid)
			p.Username = m.Username
			service.CreateProfile(p)
		}
	} else if p.Username != m.Username {
		p.Username = m.Username
		service.SaveProfile(p)
	}
	var username string
	if m.Mobile != "" {
		username = m.Mobile
	} else {
		username = p.Username
	}
	data := p.ToMap()
	data["email"] = m.Email
	data["mobile"] = m.Mobile
	data["country_code"] = m.CountryCode
	data["username"] = username
	data["avatar"] = utils.GetAvatarUri(m.Uid)
	data["regdate"] = strconv.FormatInt(m.Regdate, 10)
	if s, err := service.GetUserStatusByUid(m.Uid); err == nil {
		sdata := s.ToMap()
		data["lastlogin"] = sdata["lastlogin"]
		data["money"] = sdata["money"]
		data["bean"] = sdata["bean"]
		data["level"] = sdata["level"]
		data["experience"] = sdata["experience"]
		data["logincount"] = sdata["logincount"]
	}
	if pex, err := service.GetUserProfileExtraByUid(c, m.Uid); err == nil && pex != "" {
		m := make(map[string]interface{})
		json.Unmarshal([]byte(pex), &m)
		for k, v := range m {
			if data[k] == nil {
				data[k] = v
			}
		}
	}

	var uinfo gin.H
	if val, ok := c.Get("uinfo"); ok {
		uinfo, _ = val.(gin.H)
	}
	if uinfo == nil {
		uinfo = make(gin.H)
	}
	uinfo["username"] = username
	uinfo["realname"] = p.Realname
	uinfo["grade"] = p.Grade
	uinfo["province_id"] = p.ProvinceId
	uinfo["city_id"] = p.CityId
	uinfo["district_id"] = p.DistrictId
	uinfo["gender"] = p.Gender
	uinfo["birth_y"] = p.BirthY
	uinfo["birth_m"] = p.BirthM
	uinfo["birth_d"] = p.BirthD
	c.Set("uinfo", uinfo)

	response.OkWithData(data, c)
}

// ProfileOthers 获取其他用户个人资料
// login yes
// @Param sn string true "签名"
// @Param uids string true "用户id数组" "1,2,3"
// @Param extra string false "是否获取额外信息（学币）；1 是"
// @Success 200 {json} json {"result": []}
// @Router /profile/getOthers [get]
func ProfileOthers(c *gin.Context) {
	_, loginOK := CheckLogin(c)
	if !loginOK {
		return
	}
	uids := GetRequestParam(c, "uids")
	if !utils.TestUids(uids) {
		response.FailWithErrcode(response.E_PARAM, "用户id数组不合法", c)
		return
	}
	var extra bool
	if GetRequestParam(c, "extra") == "1" {
		extra = true
	}
	all, _ := service.GetProfileByUids(uids)
	var status_map map[int]map[string]interface{}
	if extra {
		status_map = make(map[int]map[string]interface{})
		s, _ := service.GetUserStatusByUids(uids)
		for i := range s {
			status_map[s[i].Uid] = s[i].ToMap()
		}
	}
	data := make([]gin.H, 0, len(all))
	for _, val := range all {
		ret := val.ToMap()
		ret["avatar"] = utils.GetAvatarUri(uint(val.Uid))
		if extra {
			ret["status"] = status_map[val.Uid]
		}
		data = append(data, ret)
	}
	response.OkWithData(gin.H{"result": data}, c)
}

// GetOthersProfile 获取其他用户简略信息
// login no
// @Param sn string false "签名"
// @Param uids string true "用户id数组（len < 20）" "1,2,3"
// @Param extra string false "额外信息（手机号）"
// @Success 200 {json} json {"result": [{"uid": "", "username": "", "realname": "", "avatar": "", "school": ""}]}
// @Router /profile/getOthersSimple [get]
func ProfileOthersSimple(c *gin.Context) {
	uids := GetRequestParam(c, "uids")
	if !utils.TestUids(uids) {
		response.FailWithErrcode(response.E_PARAM, "用户id数组不合法", c)
		return
	}
	if len(strings.Split(uids, ",")) > 20 {
		response.FailWithErrcode(response.E_PARAM, "参数越界", c)
		return
	}
	var extra bool
	if GetRequestParam(c, "extra") == "1" {
		extra = true
	}
	var extra_data map[int]model.AcMember
	var signed bool
	if extra {
		extra_data = make(map[int]model.AcMember)
		reason, _, _ := CheckSignature(c)
		if reason >= 0 {
			signed = true
		}
		list, _ := service.GetMemberByUids(uids)
		for i := range list {
			extra_data[int(list[i].Uid)] = list[i]
		}
	}
	all, _ := service.GetProfileByUids(uids)
	data := make([]gin.H, 0, len(all))
	for i := range all {
		ret := gin.H{
			"uid":         strconv.Itoa(all[i].Uid),
			"username":    all[i].Username,
			"realname":    all[i].Realname,
			"avatar":      utils.GetAvatarUri(uint(all[i].Uid)),
			"school":      all[i].School,
			"province_id": all[i].ProvinceId,
			"city_id":     all[i].CityId,
			"grade":       all[i].Grade,
		}
		if extra {
			if m, ok := extra_data[all[i].Uid]; ok {
				username := all[i].Username
				mobile := m.Mobile
				if !signed {
					if username == mobile {
						username = utils.MaskMobile(mobile)
					}
					mobile = utils.MaskMobile(mobile)
				}
				ret["username"] = username
				ret["mobile"] = mobile
			}
		}
		data = append(data, ret)
	}
	response.OkWithData(gin.H{"result": data}, c)
}

// ProfileEdit 编辑用户资料
// login yes
// @Param sn string true "签名"
// @Param model.UserPorfile model.UserPorfile true "用户资料"
// @Success 200 {json} json {"success": true, }
// @Router /profile/edit [get]
func ProfileEdit(c *gin.Context) {
	m, loginOk := CheckLogin(c)
	if !loginOk {
		return
	}
	var req model.UserProfile
	err1 := c.ShouldBind(&req)
	err2 := c.ShouldBindQuery(&req)
	if err1 != nil || err2 != nil {
		var verrs validator.ValidationErrors
		if v, ok := err1.(validator.ValidationErrors); ok {
			verrs = append(verrs, v...)
		}
		if v, ok := err2.(validator.ValidationErrors); ok {
			verrs = append(verrs, v...)
		}
		if len(verrs) > 0 {
			response.FailWithErrcode(response.E_PARAM, req.GetError(verrs), c)
			return
		}
	}
	p, err := service.GetProfileByUid(m.Uid)
	var success bool
	// 开启编辑限制
	var extra bool
	var limitTypes []int
	if GetRequestParam(c, "extra") == "1" {
		// 非样机 extra 才生效
		if serial := GetDeviceSerial(c); serial == "" || !utils.IsExampleMachine(serial) {
			extra = true
			limitTypes = getProfileLimit(c, p, &req)
		}
	}

	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		req.Uid = int(m.Uid)
		req.Username = m.Username
		if err = service.CreateProfile(&req); err == nil {
			p = &req
			success = true
		}
	} else {
		// 非个人中心或开启了编辑限制的，暂时禁止编辑昵称和自定义学校
		if appid := c.GetString("appid"); appid != "com.readboy.personalsetting" || GetRequestParam(c, "extra") == "1" {
			if p.Realname != req.Realname && p.Realname != "" && req.Realname != "" {
				if ok := service.CheckProfileLimitConfig(model.ProfileLimitConfigTypeNickname, c.ClientIP()); ok {
					response.FailWithErrcode(response.E_SEVER_CLOSED, "由于系统维护，请稍后再试", c)
					return
				}
			}
			if GetRequestParam(c, "custom_school") != "" && p.School != "" && p.School != req.School {
				if ok := service.CheckProfileLimitConfig(model.ProfileLimitConfigTypeSchool, c.ClientIP()); ok {
					response.FailWithErrcode(response.E_SEVER_CLOSED, "由于系统维护，请稍后再试", c)
					return
				}
			}
		}
		if needUpdateProfile(c, p, &req) {
			if err = service.SaveProfile(p); err == nil {
				success = true
			}
		} else {
			success = true
		}
	}
	data := gin.H{
		"success": success,
	}
	if success && p.Uid > 0 {
		data = p.ToMap()
		data["avatar"] = utils.GetAvatarUri(uint(p.Uid))
		data["success"] = success
		if extra {
			for _, t := range limitTypes {
				if service.CreateProfileLimit(p.Uid, t) {
					switch t {
					case model.ProfileLimitRealname, model.ProfileLimitNickname:
						data["realname_limit"] = global.PROFILE_EDIT_LIMIT
					case model.ProfileLimitSchool:
						data["school_limit"] = global.PROFILE_EDIT_LIMIT
					}
				}
			}
		}
	}
	response.OkWithData(data, c)
}

// needUpdateProfile 判断是否有更新
func needUpdateProfile(c *gin.Context, p, req *model.UserProfile) bool {
	var needUpdate bool
	if req.Realname != "" && p.Realname != req.Realname {
		p.Realname = req.Realname
		needUpdate = true
	}
	if GetRequestParam(c, "gender") != "" && p.Gender != req.Gender {
		p.Gender = req.Gender
		needUpdate = true
	}
	if req.BirthY > 0 && p.BirthY != req.BirthY {
		p.BirthY = req.BirthY
		needUpdate = true
	}
	if req.BirthM > 0 && p.BirthM != req.BirthM {
		p.BirthM = req.BirthM
		needUpdate = true
	}
	if req.BirthD > 0 && p.BirthD != req.BirthD {
		p.BirthD = req.BirthD
		needUpdate = true
	}
	if req.Height > 0 && p.Height != req.Height {
		p.Height = req.Height
		needUpdate = true
	}
	if req.Weight > 0 && p.Weight != req.Weight {
		p.Weight = req.Weight
		needUpdate = true
	}
	if req.Constellation != "" && p.Constellation != req.Constellation {
		p.Constellation = req.Constellation
		needUpdate = true
	}
	if req.Bloodtype > 0 && p.Bloodtype != req.Bloodtype {
		p.Bloodtype = req.Bloodtype
		needUpdate = true
	}
	if req.Grade > 0 && p.Grade != req.Grade {
		p.Grade = req.Grade
		needUpdate = true
	}
	if req.ProvinceId > 0 && p.ProvinceId != req.ProvinceId {
		p.ProvinceId = req.ProvinceId
		needUpdate = true
	}
	if req.CityId > 0 && p.CityId != req.CityId {
		p.CityId = req.CityId
		needUpdate = true
	}
	if req.DistrictId > 0 && p.DistrictId != req.DistrictId {
		p.DistrictId = req.DistrictId
		needUpdate = true
	}
	if req.Address != "" && p.Address != req.Address {
		p.Address = req.Address
		needUpdate = true
	}
	if req.School != "" && p.School != req.School {
		p.School = req.School
		needUpdate = true
	}
	if req.Sign != "" && p.Sign != req.Sign {
		p.Sign = req.Sign
		needUpdate = true
	}
	if req.Explain != "" && p.Explain != req.Explain {
		p.Explain = req.Explain
		needUpdate = true
	}
	if req.QQ != "" && p.QQ != req.QQ {
		p.QQ = req.QQ
		needUpdate = true
	}
	if req.Weixin != "" && p.Weixin != req.Weixin {
		p.Weixin = req.Weixin
		needUpdate = true
	}
	if req.Weibo != "" && p.Weibo != req.Weibo {
		p.Weibo = req.Weibo
		needUpdate = true
	}

	return needUpdate
}

func getProfileLimit(c *gin.Context, p, req *model.UserProfile) []int {
	limitTypes := make([]int, 0, 2)
	if req.Realname != "" && req.Realname != p.Realname {
		deviceModel := GetDeviceModel(c)
		t := model.ProfileLimitRealname
		if deviceModel != "" && utils.InStringSlice(deviceModel, global.VIPER.GetStringSlice("profile_limit.model")) {
			t = model.ProfileLimitNickname
		}
		if service.CheckProfileLimit(p.Uid, t) == 0 {
			limitTypes = append(limitTypes, t)
		} else {
			req.Realname = p.Realname
		}
	}
	if req.School != "" && req.School != p.School &&
		GetRequestParam(c, "custom_school") != "" &&
		service.CheckProfileLimit(p.Uid, model.ProfileLimitSchool) == 0 {
		limitTypes = append(limitTypes, model.ProfileLimitSchool)
	} else {
		req.School = p.School
	}
	return limitTypes
}

// ProfileOthers2 获取其他用户个人资料
// login no
// @Param sn string true "签名"
// @Param uids string true "用户id数组" "1,2,3"
// @Success 200 {json} json {"result": []}
// @Router /profile/getOthers2 [get]
func ProfileOthers2(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if appid := GetAppid(c); appid != "kyb.happydinosaur.cn" {
		response.FailWithErrcode(response.E_ACCESS, "", c)
		return
	}
	uids := GetRequestParam(c, "uids")
	if !utils.TestUids(uids) {
		response.FailWithErrcode(response.E_PARAM, "用户id数组不合法", c)
		return
	}
	all, _ := service.GetProfileByUids(uids)
	data := make([]gin.H, 0, len(all))
	for _, val := range all {
		ret := val.ToMap()
		ret["avatar"] = utils.GetAvatarUri(uint(val.Uid))
		data = append(data, ret)
	}
	response.OkWithData(gin.H{"result": data}, c)
}
