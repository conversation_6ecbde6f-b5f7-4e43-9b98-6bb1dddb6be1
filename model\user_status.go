package model

import "strconv"

type UserStatus struct {
	Uid        int `gorm:"primarykey"`
	Lastip     string
	Lastlogin  int64
	Lastapp    string
	Logincount int
	Money      float64
	Bean       int
	Level      int
	Experience int
}

func (UserStatus) TableName() string {
	return "user_status"
}

func (s UserStatus) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"uid":        strconv.Itoa(s.Uid),
		"lastip":     s.Last<PERSON>,
		"lastlogin":  strconv.FormatInt(s.Lastlogin, 10),
		"logincount": strconv.Itoa(s.Logincount),
		"money":      strconv.FormatFloat(s.Money, 'f', 2, 64),
		"bean":       strconv.Itoa(s.<PERSON>),
		"level":      strconv.Itoa(s.Level),
		"experience": strconv.Itoa(s.Experience),
	}
}
