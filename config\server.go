package config

type Server struct {
	App   App         `mapstructure:"app" json:"app" yaml:"app"`
	Zap   Zap         `mapstructure:"zap" json:"zap" yaml:"zap"`
	Mysql Mysql       `mapstructure:"mysql" json:"mysql" yaml:"mysql"`
	Oss   Oss         `mapstructure:"oss" json:"oss" yaml:"oss"`
	MQ    MQ          `mapstructure:"mq"`
	Ebag  Ebag        `mapstructure:"ebag"`
	Email Email       `mapstructure:"email"`
	Ctdid CtdidConfig `mapstructure:"ctdid" json:"ctdid" yaml:"ctdid"`
}
