import psutil
import sys
import os
import signal

basedir = '/data/www/gin-account'

def kill(pid):
    try:
        a = os.kill(pid, signal.SIGKILL)
        #print('pid:%s,　return:%s' % (pid, a))
        return 0
    except OSError:
        print('Error!!!')
        return -1
   
if __name__ == "__main__":
    os.chdir(os.path.join(basedir, 'workers/area'))
    kill_exists = True
    scripts = ["areaservice.py"]

    if kill_exists:
        for proc in psutil.process_iter():
            try:
                if proc.name() == 'python3':
                    cmdline = psutil.Process(proc.pid).cmdline()
                    
                    if len(cmdline) > 2:
                        if cmdline[2] == sys.argv[0]:continue
                        if cmdline[2] in scripts:
                            print('Killing:', ' '.join(cmdline), proc.pid, kill(proc.pid))
                    elif len(cmdline) > 1:
                        if cmdline[1] == sys.argv[0]:continue
                        if cmdline[1] in scripts:
                            print('Killing:', ' '.join(cmdline), proc.pid, kill(proc.pid))
                            
            except Exception as e:
                print(e)
    
    logdir = '/data/logs'
    
    if not os.path.exists(logdir):
        os.makedirs(logdir)
        
    for script in scripts:
        cmdline = "nohup python3 -u %s >> %s/area.log 2>&1 &" % (script, logdir)
        ret = os.system(cmdline)
        print("Starting:", cmdline, ret)
