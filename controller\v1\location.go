package v1

import (
	"account/global"
	"account/model/response"
	"account/service"
	"account/utils"
	"encoding/json"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// LocationIndex 查看设备和设备定位信息
// @Param sn string true "签名"
// @Param serial string true "设备序列号"
// @Param asc 【未知】
// @Success {json} json {model.AcDevice, "locations": [{"loctime": "2006-01-02 15:04:05", "loctype": 1, "loc": "", "locdesp": ""}]}
// @Router /location [get]
func LocationIndex(c *gin.Context) {
	reason, _, _ := CheckSignature(c)
	if reason < 0 {
		response.Fail(reason, "Access Denied", c)
		return
	}
	serial := GetRequestParam(c, "serial")
	asc := GetRequestParam(c, "asc")
	if serial == "" {
		response.Fail(-4, "Bad param", c)
		return
	}
	d, err := service.GetDeviceBySerial(serial)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.Fail(-3, "No such device", c)
		} else {
			response.Fail(-3, "Database error", c)
		}
		return
	}
	data := utils.FilterNull(utils.StructToUnderscoreMap(*d))
	data["id"] = strconv.Itoa(int(d.ID))
	locations := make([]gin.H, 0, 10)
	if asc != "" {
		if duniq, err := service.GetDeviceUniqByUniqno(serial); err == nil {
			locations = append(locations, gin.H{
				"loctime": duniq.CreatedAt.Format(global.TIME_LAYOUT_DATECLOCK),
				"loctype": 1,
				"loc":     duniq.Location,
				"locdesp": getAddresOf(duniq.Location),
			})
		}
	} else {
		if l, err := service.GetLocationById(int(d.ID)); err == nil {
			lastlocs := strings.Split(l.Lastlocs, ";")
			for _, val := range lastlocs {
				ss := strings.Split(val, ",")
				if len(ss) < 3 {
					continue
				}
				timestr := ss[0]
				loc := ss[1] + "," + ss[2]
				t, _ := strconv.ParseInt(timestr, 10, 64)
				locations = append(locations, gin.H{
					"loctime": time.Unix(t, 0).Format(global.TIME_LAYOUT_DATECLOCK),
					"loctype": 1,
					"loc":     loc,
					"desp":    getAddresOf(loc),
				})
			}
		}
	}
	data["locations"] = locations
	response.OkWithData(data, c)
}

func getAddresOf(location string) interface{} {
	if location == "" {
		return location
	}
	resp, err := utils.SendGet("http://restapi.amap.com/v3/geocode/regeo?key=45a9088831d2d9e147f75f602a5b3554&radius=1000&extensions=base&batch=false&roadlevel=1&location=" + location)
	if err == nil {
		var m map[string]interface{}
		json.Unmarshal(resp, &m)
		if status, ok := m["status"].(string); ok && status == "1" {
			if regeocode, ok := m["regeocode"].(map[string]interface{}); ok {
				return regeocode["formatted_address"]
			}
		}
	}
	return location
}

// LocationFind 查看设备和设备定位信息（无需签名）
// @Param serial string true "设备序列号"
// @Param asc 【未知】
// @Success {json} json {model.AcDevice, "locations": [{"loctime": "2006-01-02 15:04:05", "loctype": 1, "loc": "", "locdesp": ""}]}
// @Router /location/find [get]
func LocationFind(c *gin.Context) {
	serial := GetRequestParam(c, "serial")
	asc := GetRequestParam(c, "asc")
	if serial == "" {
		response.Fail(-4, "Bad param", c)
		return
	}
	d, err := service.GetDeviceBySerial(serial)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.Fail(-3, "No such device", c)
		} else {
			response.Fail(-3, "Database error", c)
		}
		return
	}
	data := utils.FilterNull(utils.StructToUnderscoreMap(*d))
	locations := make([]gin.H, 0, 10)
	if asc != "" {
		if duniq, err := service.GetDeviceUniqByUniqno(serial); err == nil {
			locations = append(locations, gin.H{
				"loctime": duniq.CreatedAt.Format(global.TIME_LAYOUT_DATECLOCK),
				"loctype": 1,
				"loc":     duniq.Location,
				"locdesp": getAddresOf(duniq.Location),
			})
		}
	} else {
		if l, err := service.GetLocationById(int(d.ID)); err == nil {
			lastlocs := strings.Split(l.Lastlocs, ";")
			for _, val := range lastlocs {
				ss := strings.Split(val, ",")
				if len(ss) < 3 {
					continue
				}
				timestr := ss[0]
				loc := ss[1] + "," + ss[2]
				t, _ := strconv.ParseInt(timestr, 10, 64)
				locations = append(locations, gin.H{
					"loctime": time.Unix(t, 0).Format(global.TIME_LAYOUT_DATECLOCK),
					"loctype": 1,
					"loc":     loc,
					"desp":    getAddresOf(loc),
				})
			}
		}
	}
	data["locations"] = locations
	response.OkWithData(data, c)
}
