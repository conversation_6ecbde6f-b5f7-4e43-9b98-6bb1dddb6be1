package response

import "fmt"

type IError struct {
	Code    int
	Message string
	Err     error
}

func (ie *IError) Error() string {
	return fmt.Sprintf("Code: %v, Message: %v, error: %v", ie.Code, ie.Message, ie.Err)
}

func New(code int, msg string, err error) *IError {
	if code <= 0 {
		code = E_UNKNOWN
	}
	if msg == "" {
		msg = Errmsg(&code)
	}
	return &IError{
		Code:    code,
		Message: msg,
		Err:     err,
	}
}
