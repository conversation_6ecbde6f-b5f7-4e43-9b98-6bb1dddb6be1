package v1

import (
	"account/global"
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

/*
400 服务接口
url https://account.readboy.com/service/xxxx?sn=<签名>&key=xxx
param:
  sn = appid + "-" + t + "-" +  md5(appid + t + key + appsec)
  	t：时间戳
	appid："Readboy.400"
	appsec："0808249bcb080b2309738050381cd830"
  key：参数
*/

var serviceapps = map[string]string{
	"Readboy.400": "0808249bcb080b2309738050381cd830",
}

// service 相关接口签名校验函数
func service_checkSN(c *gin.Context) (key string, ok bool) {
	sn := GetRequestParam(c, "sn")
	if sn == "" {
		response.FailWithErrcode(response.E_INVALID_SN, "签名为空", c)
		return
	}
	key = GetRequestParam(c, "key")
	if key == "" {
		response.FailWithErrcode(response.E_INVALID_SN, "无效参数", c)
		return
	}
	var (
		appid, timestr, md5str string
	)
	if ss := strings.Split(sn, "-"); len(ss) != 3 {
		response.FailWithErrcode(response.E_INVALID_SN, "签名格式错误", c)
		return
	} else {
		appid, timestr, md5str = ss[0], ss[1], ss[2]
	}
	if appid == "" || timestr == "" {
		response.FailWithErrcode(response.E_INVALID_SN, "签名格式错误", c)
		return
	}
	appsec, exists := serviceapps[appid]
	if !exists {
		response.FailWithErrcode(response.E_INVALID_SN, "签名无效", c)
		return
	}
	timestamp, _ := strconv.ParseInt(timestr, 10, 64)
	now := time.Now().Unix()
	if elapsed := now - timestamp; elapsed > 3600 || elapsed < -3600 {
		response.FailWithErrcode(response.E_INVALID_SN, "签名过期", c)
		return
	}
	if sign := utils.MD5(appid + timestr + key + appsec); sign != md5str {
		response.FailWithErrcode(response.E_INVALID_SN, "签名错误", c)
		return
	}
	ok = true
	return
}

// ServiceForbidden 禁用账号
// @Param sn string true "签名"
// @Param key string true "参数"
// @Success {json} json {"key": "", "forbidden": 1, "success": true} "success 为 false 时，forbidden 为该账号当前状态"
// @Router /service/forbidden [get]
func ServiceForbidden(c *gin.Context) {
	key, ok := service_checkSN(c)
	if !ok {
		return
	}
	m, err := service.GetMemberByAccount(key)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_PARAM, "无法找到账号", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	forbidden := m.Forbidden
	status := (service.SetMemberForbidden(int(m.Uid), 1) == nil)
	if status {
		forbidden = 1
	}
	response.OkWithData(gin.H{
		"forbidden": forbidden,
		"success":   status,
		"key":       key,
	}, c)
}

// ServicePermit 启用账号
// @Param sn string true "签名"
// @Param key string true "账号"
// @Success {json} json {"key": "", "forbidden": 0, "success": true} "success 为 false 时，forbidden 为该账号当前状态"
// @Router /service/permit [get]
func ServicePermit(c *gin.Context) {
	key, ok := service_checkSN(c)
	if !ok {
		return
	}
	m, err := service.GetMemberByAccount(key)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_PARAM, "无法找到账号", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	forbidden := m.Forbidden
	status := (service.SetMemberForbidden(int(m.Uid), 0) == nil)
	if status {
		forbidden = 0
	}
	response.OkWithData(gin.H{
		"forbidden": forbidden,
		"success":   status,
		"key":       key,
	}, c)
}

// ServiceResetpwd 重置密码
// @Param sn string true "签名"
// @Param key string true "账号"
// @Success {json} json {"key": "", "password": "", "success": true}
// @Router /service/resetpswd [get]
func ServiceResetpwd(c *gin.Context) {
	key, ok := service_checkSN(c)
	if !ok {
		return
	}
	m, err := service.GetMemberByAccount(key)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_PARAM, "无法找到账号", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	password := rand.Intn(900000) + 100000
	pwdstr := strconv.Itoa(password)
	service.FormatMemberPassword(m, utils.MD5(pwdstr))
	now := time.Now().Unix()
	timestr := strconv.FormatInt(now, 10)
	m.AccessToken = utils.MD5(key + timestr)
	m.AccessExpire = now + global.TOKEN_EXPIRE
	status := (service.ResetPassword(m) == nil)
	response.OkWithData(gin.H{
		"password": password,
		"success":  status,
		"key":      key,
	}, c)
}

// ServiceUserInfo 获取用户信息
// @Param sn string true "签名"
// @Param key string true "账号"
// @Success {json} json {"uid":"", "username": "", "email": "", "mobile": "", "forbidden": "0", status: 0, "profile": model.userProfile}
// @Router /service/userinfo [get]
func ServiceUserinfo(c *gin.Context) {
	key, ok := service_checkSN(c)
	if !ok {
		return
	}
	if strings.Contains(key, "@") && !utils.TestEmail(key) {
		response.OkWithData(gin.H{"status": -2}, c)
		return
	}
	m, err := service.GetMemberByAccount(key)
	if err != nil {
		response.OkWithData(gin.H{"status": -1}, c)
		return
	}
	data := gin.H{
		"status":    m.Forbidden,
		"uid":       strconv.Itoa(int(m.Uid)),
		"username":  m.Username,
		"email":     m.Email,
		"mobile":    m.Mobile,
		"forbidden": strconv.Itoa(m.Forbidden),
	}
	p, _ := service.GetProfileByUid(m.Uid)
	if p.Uid > 0 {
		data["profile"] = p.ToMap()
	}
	response.OkWithData(data, c)
}
