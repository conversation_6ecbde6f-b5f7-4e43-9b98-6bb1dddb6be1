package utils

import (
	"account/global"
	"crypto/tls"
	"html/template"
	"log"
	"os"
	"strings"
	"time"

	"github.com/go-gomail/gomail"
	"go.uber.org/zap"
)

var (
	mailLogFile = "/data/logs/mail.log"
	mailLogger  *log.Logger
)

func init() {
	InitMailLogger()
}

func InitMailLogger() error {
	f, err := os.OpenFile(mailLogFile, os.O_APPEND|os.O_RDWR|os.O_CREATE, 0777)
	if err == nil {
		if mailLogger == nil {
			mailLogger = log.New(f, "", log.LstdFlags)
		} else {
			mailLogger.SetOutput(f)
		}
	}
	return err
}

func MailLog(values ...string) {
	_, err := os.Stat(mailLogFile)
	if err != nil && os.IsNotExist(err) {
		err = InitMailLogger()
	}
	if err == nil {
		mailLogger.Println(values)
	} else {
		global.LOG.Info(strings.Join(values, " "))
		global.LOG.Error("", zap.Error(err))
	}
}

var (
	tpl_activate *template.Template
	tpl_lost_pwd *template.Template
	tpl_verify   *template.Template
)

func init() {
	var err error
	tpl_activate, err = template.New("email_activate").Parse(`<div style="border:2px solid #DDD"><div><div style="padding:14px 0 0 14px;"></div></div><div style="margin:2px 10px;">尊敬的<span style="color:#0080CC">{{.username}}</span>，您好！<p>感谢您使用{{.provider}}<p>请点击下面的链接激活您的帐号，完成注册。<p><a style="color:#00C0FF" href="{{.activateurl}}" target="_blank">{{.activateurl}}</a><br><br>(如果点击链接没反应，请复制激活链接，粘帖到浏览器地址栏后访问)<ul style="color:#666"><li>激活链接<b>{{.expire}}</b>小时内有效，超时请重新发送激活邮件。</li><li>激活链接将在您激活一次后失效。</li></ul></p><p><div style="float:right"><p>{{.provider}}<br>{{.date}}</p></div><hr style="border-style:none;border-top:1px dotted #CCC;clear:both;"><font color="gray">如您错误收到此邮件，请不要激活链接，该帐户将不会启用<br>这是系统自动发出，请不要回复，如有疑问，请联系：</font><font color="#0080CC">800-999-3685</font></p></div></div>`)
	// tpl_activate, err = template.ParseFiles(path.Join(wkdir, "template/activate.gtpl"))
	if err != nil {
		panic(err)
	}
	tpl_lost_pwd, err = template.New("email_lostpwd").Parse(`<div style="border:2px solid #DDD"><div><div style="padding:14px 0 0 14px;"></div></div><div style="margin:2px 10px;">尊敬的<span style="color:#0080CC">{{.username}}</span>，您好！<p>感谢您使用{{.provider}}<br><span>您的密码找回验证码为：<em style="color:#C40">{{.verify}}</em>, 序列号为：<em style="color:#333">{{.serial}}</em>,请尽快重置您的密码！</span><br><br><ul style="color:#666"><li>重置密码<b>{{.expire}}</b>小时内有效，超时请重新发送找回密码邮件。</li><li>验证码将在您使用后失效。</li></ul></p><p><div style="float:right"><p>{{.provider}}<br>{{.date}}</p></div><hr style="border-style:none;border-top:1px dotted #CCC;clear:both;"><font color="gray">如您错误收到此邮件，请忽略。<br>这是系统自动发出，请不要回复，如有疑问，请联系：</font><font color="#0080CC">800-999-3685</font></p></div></div>`)
	if err != nil {
		panic(err)
	}
	tpl_verify, err = template.New("email_verify").Parse(`<div style="border:2px solid #DDD"><div><div style="padding:14px 0 0 14px;"></div></div><div style="margin:2px 10px;">尊敬的<span style="color:#0080CC">{{.username}}</span>，您好！<p>感谢您使用{{.provider}}<br><span>您的邮箱验证码为：<em style="color:#C40">{{.verify}}</em>, 序列号为：<em style="color:#333">{{.serial}}</em>,请尽快完成验证操作！</span><br><br><ul style="color:#666"><li>验证<b>{{.expire}}</b>小时内有效，超时请重新发送验证邮件。</li><li>验证码将在您使用后失效。</li></ul></p><p><div style="float:right"><p>{{.provider}}<br>{{.date}}</p></div><hr style="border-style:none;border-top:1px dotted #CCC;clear:both;"><font color="gray">如您错误收到此邮件，请忽略。<br>这是系统自动发出，请不要回复，如有疑问，请联系：</font><font color="#0080CC">800-999-3685</font></p></div></div>`)
	if err != nil {
		panic(err)
	}
}

func SendEmailActivate(appid, email string, expire int, activateUrl string) bool {
	body := strings.Builder{}
	provider := getProvider(appid)
	err := tpl_activate.Execute(&body, map[string]interface{}{
		"provider":    provider,
		"date":        time.Now().Format("2006-01-02 15:04"),
		"username":    email,
		"activateurl": activateUrl,
		"expire":      expire,
	})
	subject := "账号激活"
	if err == nil {
		err = SendMail(email, subject, provider, body.String())
	}
	result := "Success"
	if err != nil {
		result = err.Error()
	}
	MailLog("[", appid, email, activateUrl, "]", subject, result)
	return err == nil
}

func SendEmailLostPassword(appid, email, verify, serial string, expire int64) bool {
	body := strings.Builder{}
	provider := getProvider(appid)
	err := tpl_lost_pwd.Execute(&body, map[string]interface{}{
		"provider": provider,
		"date":     time.Now().Format("2006-01-02 15:04"),
		"username": email,
		"expire":   expire,
		"serial":   serial,
		"verify":   verify,
	})
	subject := "密码重置"
	if err == nil {
		err = SendMail(email, subject, provider, body.String())
	}
	result := "Success"
	if err != nil {
		result = err.Error()
	}
	MailLog("[", appid, email, serial, verify, "]", subject, result)
	return err == nil
}

func SendEmailVerify(appid, email, verify, serial string, expire int64) bool {
	body := strings.Builder{}
	provider := getProvider(appid)
	err := tpl_verify.Execute(&body, map[string]interface{}{
		"provider": provider,
		"date":     time.Now().Format("2006-01-02 15:04"),
		"username": email,
		"expire":   expire,
		"serial":   serial,
		"verify":   verify,
	})
	subject := "验证码"
	if err == nil {
		err = SendMail(email, subject, provider, body.String())
	}
	result := "Success"
	if err != nil {
		result = err.Error()
	}
	MailLog("[", appid, email, serial, verify, "]", subject, result)
	return err == nil
}

func SendMail(mailTo, subject, provider, body string) error {
	m := gomail.NewMessage()

	m.SetHeader("From", m.FormatAddress(global.CONFIG.Email.From, provider))
	m.SetHeader("To", mailTo)       //发送给多个用户
	m.SetHeader("Subject", subject) //设置邮件主题
	m.SetBody("text/html", body)    //设置邮件正文
	d := gomail.NewDialer(global.CONFIG.Email.Host, global.CONFIG.Email.Port,
		global.CONFIG.Email.From, global.CONFIG.Email.Password)
	d.TLSConfig = &tls.Config{ServerName: global.CONFIG.Email.Host, InsecureSkipVerify: true}
	return d.DialAndSend(m)
}

func getProvider(appid string) string {
	if strings.Contains(appid, "dream") {
		return "追梦科技"
	}
	return "Readboy Account Center"
}
