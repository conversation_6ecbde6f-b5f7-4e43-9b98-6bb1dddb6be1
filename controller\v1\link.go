package v1

import (
	"account/global"
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

/*
link 为q系列账号使用
参数及说明：
sn = appid - t - md5(appid + t + id + mobile + serial + model + appsec)
	t：时间戳, appsec = linkapps[appid], appid："Readboy_Q9.UserCenter"
mobile：手机号码
serial：机器序列号
id：Q系列账号ID
model：机型
*/

var linkapps = map[string]string{
	"Readboy_Q9.UserCenter":  "08081c2423097d8309bcb0808380503b",
	"Readboy_Q8.UserCenter":  "d3e7cae5cb967d7a68ca31c2fee017c2",
	"Readboy_Q7s.UserCenter": "ddd7533790bfa3474b5c7c19ecefec45",
}

type linkRequest struct {
	Sn     string
	Id     string
	Appid  string
	Serial string
	Model  string
	Mobile string
}

// link_checkSN link 接口使用的签名检查函数
func link_checkSN(c *gin.Context) (r linkRequest, ok bool) {
	sn := GetRequestParam(c, "sn")
	if sn == "" {
		response.FailWithErrcode(response.E_INVALID_SN, "签名为空", c)
		return
	}
	var (
		timestr string
		md5str  string
	)
	if ss := strings.Split(sn, "-"); len(ss) != 3 {
		response.FailWithErrcode(response.E_INVALID_SN, "签名错误", c)
		return
	} else {
		r.Appid = ss[0]
		timestr = ss[1]
		md5str = ss[2]
	}
	r.Mobile = GetRequestParam(c, "mobile")
	if model := GetRequestParam(c, "model"); model == "" {
		response.FailWithErrcode(response.E_INVALID_SN, "无效机型", c)
		return
	} else {
		r.Model = model
	}
	if serial := GetRequestParam(c, "serial"); serial == "" {
		response.FailWithErrcode(response.E_INVALID_SN, "无效设备号", c)
		return
	} else {
		r.Serial = serial
	}
	if id := GetRequestParam(c, "id"); id == "" {
		response.FailWithErrcode(response.E_INVALID_SN, "无效账号", c)
		return
	} else {
		r.Id = id
	}
	appsec, exists := linkapps[r.Appid]
	if !exists {
		response.FailWithErrcode(response.E_INVALID_SN, "签名无效", c)
		return
	}
	sign := utils.MD5(r.Appid + timestr + r.Id + r.Mobile + r.Serial + r.Model + appsec)
	if sign != md5str {
		response.FailWithErrcode(response.E_INVALID_SN, "签名错误", c)
		return
	}
	c.Set("appid", r.Appid)
	ok = true
	return
}

// LinkRegister
// @Param sn string true "签名"
// @Param model string true "机型"
// @Param serial string true "序列号"
// @Param id string true "Q系列账号id"
// @Param mobile string false "手机号"
// @Success 200 {json} json {"uid": 1, "username": "", "forbidden": 0, "access_token": "", "access_expire": 1234567890}
// @Router /link [get]
func LinkRegister(c *gin.Context) {
	r, ok := link_checkSN(c)
	if !ok {
		return
	}
	if len(r.Id) < 2 {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	ebagid := "link-" + r.Id
	uniqno := "-i" + r.Model + "-s" + r.Serial
	if r.Mobile != "" {
		c.Set("dinfo", gin.H{
			"model": r.Model, "serial": r.Serial, "uniqno": uniqno, "origin": r.Mobile,
			"release": "", "version": "", "did": "", "mac": "",
		})
	}
	m, err := service.GetMemberByUsername(ebagid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			m.Username = ebagid
			service.FormatMemberPassword(m, "87650000")
			now := time.Now().Unix()
			timestr := strconv.FormatInt(now, 10)
			m.AccessToken = utils.MD5(ebagid + timestr)
			m.AccessExpire = now + global.TOKEN_EXPIRE
			m.Regip = GetIp(c)
			m.Regdate = now
			m.Regapp = r.Appid
			m.Forbidden = 0
			err = service.CreateMember(m)
		}
		if err != nil {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
	}
	c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": r.Mobile, "regdate": m.Regdate})
	response.OkWithData(getMemberLoginInfo(m), c)
}

// LinkUnregister
// @Param sn string true "签名"
// @Param model string true "机型"
// @Param serial string true "序列号"
// @Param id string true "Q系列账号id"
// @Param mobile string false "手机号"
// @Success {json} json {"uid": 123}
// @Router /link/unregister [get]
func LinkUnregister(c *gin.Context) {
	r, ok := link_checkSN(c)
	if !ok {
		return
	}
	if len(r.Id) < 2 {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	ebagid := "link-" + r.Id
	m, err := service.GetMemberByUsername(ebagid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	service.DeleteProfileByUid(m.Uid)
	if service.DeleteMemberByUid(m.Uid) != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	} else {
		response.OkWithData(gin.H{"uid": strconv.Itoa(int(m.Uid))}, c)
	}
}

// LinkCheck 电子书包id检验【注：未见调用，可能已弃用】
// login no
// @Param sn string true "签名"
// @Param ebagid string true "电子书包id"
// @Success 200 {json} json {"uid": 1, "username": "", "forbidden": 0, "access_token": "", "access_expire": 1234567890}
// @Router /link/check [get]
func LinkCheck(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	ebagid := GetRequestParam(c, "ebagid")
	if len(ebagid) != 36 {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	ebagid = "link-" + ebagid
	m, err := service.GetMemberByUsername(ebagid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile, "regdate": m.Regdate})
	response.OkWithData(getMemberLoginInfo(m), c)
}
