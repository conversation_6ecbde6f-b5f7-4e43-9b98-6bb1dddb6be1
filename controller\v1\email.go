package v1

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// EmailRegister 邮箱注册
// login no
// @Param sn string true "签名"
// @Param email string true "邮箱"
// @Param username string false "用户名" // 为空时为邮箱
// @Param password string true "密码"
// @Success 200 {json} json {"uid": 1, "username": "", "email": "", "forbidden": 0, "access_token": "", "access_expire": **********, "email_status": true}
// @Router /email/register [get]
func EmailRegister(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	// 清理过期验证码
	service.DropVerify()
	appid := GetAppid(c)
	email, ok := GetAndCheckEmail(c)
	if !ok {
		return
	}
	username := GetRequestParam(c, "username")
	if username == "" {
		username = email
	} else if !utils.TestUsername(username) {
		response.FailWithErrcode(response.E_USERNAME_FMT, "", c)
		return
	}
	password, ok := GetAndCheckPassword(c)
	if !ok {
		return
	}
	if service.MemberIsExistsByUsernameAndEmail(username, email) {
		response.FailWithErrcode(response.E_MEMBER_ALREADY_EXISTED, "", c)
		return
	}
	var m model.AcMember
	m.Username = username
	service.FormatMemberPassword(&m, password)
	now := time.Now().Unix()
	m.AccessToken = utils.MD5(username + strconv.FormatInt(now, 10))
	m.AccessExpire = now + global.TOKEN_EXPIRE
	m.Email = email
	m.Regip = GetIp(c)
	m.Regdate = now
	m.Regapp = appid
	m.Forbidden = 4
	err := service.CreateMember(&m)
	if err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	status := false
	if _, ok := sendEmailActivate(c, email).(string); ok {
		status = true
	}
	data := getMemberLoginInfo(&m)
	data["email"] = m.Email
	data["email_status"] = status
	response.OkWithData(data, c)
}

// EmailResetPassword 邮箱重置密码
// login no
// @Param sn string true "签名"
// @Param email string true "邮箱"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Param password string true "新密码"
// @Success 200 {json} json {"access_token": "", "access_expire": **********, "status": "success"}
// @Router /email/resetpwd [get]
func EmailResetPassword(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	email, ok := GetAndCheckEmail(c)
	if !ok {
		return
	}
	password, ok := GetAndCheckPassword(c)
	if !ok {
		return
	}
	m, err := service.GetMemberByEmail(email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "该邮箱未注册", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	if m.Forbidden == 1 {
		response.FailWithErrcode(response.E_FORBIDDEN, "", c)
		return
	}
	if !CheckVerify(c, email) {
		return
	}
	now := time.Now().Unix()
	service.FormatMemberPassword(m, password)
	m.AccessToken = utils.MD5(email + strconv.FormatInt(now, 10))
	m.AccessExpire = now + global.TOKEN_EXPIRE
	err = service.ResetPassword(m)
	if err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	response.OkWithData(gin.H{
		"access_token": m.AccessToken, "access_expire": m.AccessExpire, "status": "success",
	}, c)
}

// EmailVerify 发送邮箱验证码
// login no
// @Param sn string true "签名"
// @Param email string true "邮箱"
// @Param type int true "类型，0 密码找回；1 注册"
// @Success 200 {json} json {"serial": "", "status": true}
// @Router /email/verify [get]
func EmailVerify(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	email, ok := GetAndCheckEmail(c)
	if !ok {
		return
	}
	t := GetRequestParam(c, "type")
	m, _ := service.GetMemberByEmail(email)
	if t != "1" {
		if m.Uid == 0 {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
			return
		}
		if !CheckMemberForbidden(c, m) {
			return
		}
	} else if m.Uid > 0 {
		response.FailWithErrcode(response.E_MEMBER_ALREADY_EXISTED, "", c)
		return
	}
	now := time.Now().Unix()
	if !service.CheckVerifyLimit(email, now, 1) {
		response.FailWithErrcode(response.E_FREQUENTLY, "", c)
		return
	}
	var v model.AcVerify
	v.Username = email
	v.Verify = strconv.Itoa(rand.Intn(900000) + 100000)
	v.Expire = now + global.EMAIL_VAILD
	v.Status = 1
	v.Type = 1
	err := service.CreateVerify(&v)
	if err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	serial := strconv.Itoa(int(v.ID))
	status := false
	appid := GetAppid(c)
	if t == "1" {
		status = utils.SendEmailVerify(appid, email, v.Verify, serial, global.EMAIL_VAILD/3600)
	} else {
		status = utils.SendEmailLostPassword(appid, email, v.Verify, serial, global.EMAIL_VAILD/3600)
	}
	if status {
		response.OkWithData(gin.H{"serial": serial, "status": status}, c)
	} else {
		response.FailWithErrcode(response.E_UNKNOWN, "邮件发送失败", c)
	}
}

// EmailReActivate 重发激活邮件
// login no
// @Param sn string true "签名"
// @Param email string true "邮箱"
// @Success 200 {json} json {"serial": ""}
// @Router /email/reactivate [get]
func EmailReActivate(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	email, ok := GetAndCheckEmail(c)
	if !ok {
		return
	}
	now := time.Now().Unix()
	if !service.CheckVerifyLimit(email, now, 0) {
		response.FailWithErrcode(response.E_FREQUENTLY, "", c)
		return
	}
	m, err := service.GetMemberByEmail(email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	if m.Forbidden == 1 {
		response.FailWithErrcode(response.E_FORBIDDEN, "", c)
		return
	}
	if m.Forbidden != 4 {
		response.FailWithErrcode(response.E_ALREADY_ACTIVATED, "", c)
		return
	}
	if serial, ok := sendEmailActivate(c, email).(string); ok {
		response.OkWithData(gin.H{"serial": serial}, c)
	} else {
		return
	}
}

// sendEmailActivate 发送激活邮件，成功返回验证码序列号；否则返回false
func sendEmailActivate(c *gin.Context, email string) (result interface{}) {
	now := time.Now().Unix()
	timestr := strconv.FormatInt(now, 10)
	if !service.CheckVerifyLimit(email, now, 0) {
		response.FailWithErrcode(response.E_FREQUENTLY, "", c)
		return false
	}
	var v model.AcVerify
	v.Username = email
	v.Verify = strconv.Itoa(rand.Intn(900000) + 100000)
	v.Expire = now + global.EMAIL_VAILD
	v.Status = 1
	v.Type = 0
	err := service.CreateVerify(&v)
	if err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return false
	}
	var sys_appid, sys_appsec string
	if len(global.SystemApps) == 0 {
		return false
	}
	for k, v := range global.SystemApps {
		sys_appid, sys_appsec = k, v
		break
	}
	appid := GetAppid(c)
	sn := "00000000" + timestr + utils.MD5(timestr+sys_appsec+utils.MD5(sys_appid)) + sys_appid
	activateUrl := global.CONFIG.App.Domain + fmt.Sprintf(`/activate?email=%s&sn=%s&serial=%d&verify=%s&appid=%s`,
		email, sn, v.ID, v.Verify, appid)

	utils.SendEmailActivate(appid, email, global.EMAIL_VAILD/3600, activateUrl)
	return strconv.Itoa(int(v.ID))
}

// EmailBind 绑定邮箱
// login yes
// @Param sn string true "签名"
// @Param email string true "邮箱"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Success 200 {json} json {"username": "", "email": "", "status": "success"}
// @Router /email/bind [get]
func EmailBind(c *gin.Context) {
	m, ok := CheckLogin(c)
	if !ok {
		return
	}
	email, ok := GetAndCheckEmail(c)
	if !ok {
		return
	}
	_, err := service.GetMemberByEmail(email)
	if err == nil {
		response.FailWithErrcode(response.E_ALREADY_BOUND, "", c)
		return
	}
	if !CheckVerify(c, email) {
		return
	}
	if m.Username == m.Email {
		m.Username = email
	}
	m.Email = email
	if err := service.BindEmail(m); err == nil {
		response.OkWithData(gin.H{
			"status": "success", "username": m.Username, "email": m.Email,
		}, c)
	} else {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	}
}

// EmailRebind 绑定其他邮箱
// login no
// @Param sn string true "签名"
// @Param newemail string true "邮箱"
// @Param uid/email int/string true "用户id/旧邮箱（二选一）"
// @Param password string true "密码"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Router /email/rebind [get]
func EmailRebind(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	newemail := GetRequestParam(c, "newemail")
	if newemail == "" {
		response.FailWithErrcode(response.E_REQUIRE_EMAIL, "", c)
		return
	} else if !utils.TestEmail(newemail) {
		response.FailWithErrcode(response.E_EMAIL_FMT, "", c)
		return
	}
	password, ok := GetAndCheckPassword(c)
	if !ok {
		return
	}
	if _, err := service.GetMemberByEmail(newemail); err == nil {
		response.FailWithErrcode(response.E_MEMBER_ALREADY_EXISTED, "", c)
		return
	}
	if reason == 0 {
		uid := GetRequestParam(c, "uid")
		if uid == "" {
			email, ok := GetAndCheckEmail(c)
			if !ok {
				return
			}
			m, _ = service.GetMemberByEmail(email)
		} else {
			m, _ = service.GetMemberByUid(uid)
		}
	}
	if m.Uid == 0 {
		response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		return
	}
	if m.Forbidden == 1 {
		response.FailWithErrcode(response.E_FORBIDDEN, "", c)
		return
	}
	if !service.CheckPwd(m, password) {
		response.FailWithErrcode(response.E_PASSWORD, "", c)
		return
	}
	if !CheckVerify(c, newemail) {
		return
	}
	if m.Username == m.Email {
		m.Username = newemail
	}
	m.Email = newemail
	if err := service.BindEmail(m); err == nil {
		response.OkWithData(gin.H{
			"status": "success", "username": m.Username, "email": m.Email,
		}, c)
	} else {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	}
}
