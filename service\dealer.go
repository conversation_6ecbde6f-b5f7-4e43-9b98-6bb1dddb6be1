package service

import (
	"account/global"
	"account/model"
)

func GetDealerByCustomerNumber(customer_number string) (*model.Dealer, error) {
	var d model.Dealer
	err := global.DB.Model(&model.Dealer{}).First(&d, "customer_number=?", customer_number).Error
	return &d, err
}

func CreateDealer(d *model.Dealer) error {
	return global.DB.Model(&model.Dealer{}).Create(d).Error
}

func SaveDealer(d *model.Dealer) error {
	return global.DB.Model(&model.Dealer{}).Where("id=?", d.ID).Limit(1).Save(d).Error
}
