package v1

import (
	"errors"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"account/global"
	"account/model"
	"account/model/response"
	"account/pkg/cryptor"
	"account/service"
	"account/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// MemberCount 获取账号总数
// login no
// @Param sn string true "签名"
// @Success 200 {json} json {"count": 0}
// @Router /member/count [get]
func MemberCount(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if cnt, err := service.GetMemberCount(); err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	} else {
		response.OkWithData(gin.H{"count": cnt}, c)
	}
}

// MemberStatus 获取账号状态
// login no
// @Param sn string true "签名"
// @Param username string true "账号"
// @Success 200 {json} json {"status": 0, "username": "admin", "mobile": ""}
// @Return status int -2 用户名非法 -1 未注册 0 正常 1 禁用 2 特殊 4 未激活
// @Router /member/status [get]
func MemberStatus(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	username := GetRequestParam(c, "username")
	if username == "" {
		response.FailWithErrcode(response.E_REQUIRE_USERNAME, "", c)
		return
	}
	if strings.Contains(username, "@") && !utils.TestEmail(username) {
		response.OkWithData(gin.H{"status": -2}, c)
		return
	}
	m, err := service.GetMemberByAccount(username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.OkWithData(gin.H{"status": -1}, c)
		} else {
			response.FailWithDetail(response.E_DATABASE, "", gin.H{"status": -1}, c)
		}
		return
	}
	response.OkWithData(gin.H{"status": m.Forbidden, "username": m.Username, "mobile": m.Mobile}, c)
}

// MemberChpwd 修改密码
// login no
// @Param sn string true "签名"
// @Param username string true "账号，签名uid=0时必填"
// @Param oldpassword string true "旧密码"
// @Param password string true "新密码"
// @Success 200 {json} json {"status": "success"}
// @Router /member/chpwd [get]
func MemberChpwd(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	} else if reason == 0 {
		username := GetRequestParam(c, "username")
		if username == "" {
			response.FailWithErrcode(response.E_REQUIRE_USERNAME, "", c)
			return
		}
		member, err := service.GetMemberByAccount(username)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
			} else {
				response.FailWithErrcode(response.E_DATABASE, "", c)
			}
			return
		}
		m = member
	}
	if !CheckMemberForbidden(c, m) {
		return
	}
	oldpwd := GetRequestParam(c, "oldpassword")
	newpwd := GetRequestParam(c, "password")

	if !service.CheckPwd(m, oldpwd) {
		response.FailWithErrcode(response.E_PASSWORD, "", c)
		return
	}
	if err := service.ChangePassword(m, newpwd); err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	} else {
		c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
		response.OkWithData(gin.H{"status": "success"}, c)
	}
}

// MemberLogin 用户登录
// @Param sn string true "签名（可使用access_token登录）"
// @Param username string false "账号（选填。用户名，手机，邮箱，微信号）"
// @Param password string false "密码（选填）"
// @Success 200 {json} json {"uid": 1, "username": "", "email": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********}
// @Router /member/login [get]
func MemberLogin(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 && reason != -2 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	now := time.Now().Unix()
	serial := GetDeviceSerial(c)
	deviceModel := GetDeviceModel(c)
	real2 := false
	if deviceModel != "" && utils.InStringSlice(deviceModel, global.VIPER.GetStringSlice("profile_limit.model")) {
		real2 = true
	}
	// token 登录
	if reason == 1 {
		if m.Forbidden == 1 {
			response.FailWithErrcode(response.E_FORBIDDEN, "", c)
			return
		}
		// 主账号检查是否为演示账号
		if !IsSubmember(m) {
			if !CheckExampleMachine(c, m.Mobile) {
				return
			}
		}

		if !CheckDeviceLoginMember(c, m) {
			return
		}

		m.AccessExpire = now + global.TOKEN_EXPIRE

		if err := service.SaveMemberAccess(m); err != nil {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
		data := getMemberLoginInfo(m)
		data["email"] = m.Email
		data["mobile"] = m.Mobile
		data["country_code"] = m.CountryCode
		data["access_expire"] = m.AccessExpire
		data["avatar"] = utils.GetAvatarUri(m.Uid)
		data["realname_tips"] = model.RealnameTips
		if real2 {
			data["realname_tips"] = model.RealnameTips2
		}
		data["school_tips"] = model.SchoolTips
		if serial != "" && utils.IsExampleMachine(serial) {
			data["realname_limit"] = 0
			data["school_limit"] = 0
			data["realname_tips"] = ""
			data["school_tips"] = ""
		} else {
			if !real2 {
				data["realname_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitRealname)
			} else {
				data["realname_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitNickname)
			}
			data["school_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitSchool)
		}
		response.OkWithData(data, c)
		return
	}
	// 账号，密码登录
	username := GetRequestParam(c, "username")
	if encrypt, ok := GetAndCheckEncrypt(c); !ok {
		return
	} else if encrypt == 1 {
		key := c.GetString("appsec")
		if len(key) != 32 {
			key = utils.MD5(key)
		}
		username = cryptor.AesCbcDecrypt(username, key)
	}
	password := GetRequestParam(c, "password")
	if (username == "" || password == "") && reason == -2 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if username == "" {
		response.FailWithErrcode(response.E_REQUIRE_USERNAME, "", c)
		return
	}
	if password == "" {
		response.FailWithErrcode(response.E_REQUIRE_PWD, "", c)
		return
	}
	m, err := service.GetMemberByAccount(username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	if !CheckMemberForbidden(c, m) {
		return
	}
	if reachLimit, err := service.CheckLoginReachLimit(c, m); err != nil {
		c.Error(err)
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	} else if reachLimit {
		response.FailWithErrcode(response.E_LOGIN_LOCK, "", c)
		return
	}
	if !service.CheckPwd(m, password) {
		service.SetTryLogin(c, m, false)
		response.FailWithErrcode(response.E_PASSWORD, "", c)
		return
	}
	service.SetTryLogin(c, m, true)
	if !IsSubmember(m) {
		if !CheckExampleMachine(c, m.Mobile) {
			return
		}
	}

	if !CheckDeviceLoginMember(c, m) {
		return
	}

	if now >= m.AccessExpire {
		m.AccessToken = service.CreateMemberAccessToken(m.Uid, now)
	}
	m.AccessExpire = now + global.TOKEN_EXPIRE
	if err := service.SaveMemberAccess(m); err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	appid := GetAppid(c)
	// service.CreateOrUpdateUserStatus(m.Uid, c.ClientIP(), appid, now)
	if us, err := service.GetUserStatusByUid(m.Uid); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			us.Uid = int(m.Uid)
			us.Lastip = GetIp(c)
			us.Lastlogin = now
			us.Lastapp = appid
			us.Logincount = 1
			service.CreateUserStatus(us)
		}
	}
	c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
	data := getMemberLoginInfo(m)
	data["email"] = m.Email
	data["mobile"] = m.Mobile
	data["country_code"] = m.CountryCode
	data["access_expire"] = m.AccessExpire
	data["avatar"] = utils.GetAvatarUri(m.Uid)
	data["realname_tips"] = model.RealnameTips
	data["school_tips"] = model.SchoolTips
	if real2 {
		data["realname_tips"] = model.RealnameTips2
	}
	if serial != "" && utils.IsExampleMachine(serial) {
		data["realname_limit"] = 0
		data["school_limit"] = 0
		data["realname_tips"] = ""
		data["school_tips"] = ""
	} else {
		if !real2 {
			data["realname_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitRealname)
		} else {
			data["realname_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitNickname)
		}
		data["school_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitSchool)
	}
	response.OkWithData(data, c)
}

// MemberThirdRegister 第三方注册
// @Param sn       string  true "签名"
// @Param username string  true "账号"
// @Param regip    string  true "注册ip"
// @Param regapp   string  true "注册来源（企业名）"
// @Success 200 {json} json {"uid": 1, "username": "", "email": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********}
// @Router /member/third/register [get]
func MemberThirdRegister(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	username := GetRequestParam(c, "username")
	if len(username) != 36 {
		response.FailWithErrcode(response.E_PARAM, "用户名不能为空", c)
		return
	}
	regip := GetRequestParam(c, "regip")
	if regip == "" {
		response.FailWithErrcode(response.E_PARAM, "ip不能为空", c)
		return
	}
	regapp := GetRequestParam(c, "regapp")
	if regapp == "" {
		response.FailWithErrcode(response.E_PARAM, "注册来源不能为空", c)
		return
	}
	m, err := service.GetMemberByUsername(username)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		password := GetRequestParam(c, "password")
		if password == "" {
			password = strconv.Itoa(rand.Intn(10000000))
		}
		now := time.Now().Unix()
		timestr := strconv.FormatInt(now, 10)
		m.Username = username
		service.FormatMemberPassword(m, password)
		m.Forbidden = 0
		m.AccessToken = utils.MD5(username + timestr)
		m.AccessExpire = now + global.TOKEN_EXPIRE
		m.Regip = regip
		m.Regdate = now
		m.Regapp = regapp
		err = service.CreateMember(m)
	}
	if err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	response.OkWithData(getMemberLoginInfo(m), c)
}

// MemberStatus2 获取账号状态
// login no
// @Param sn string true "签名，checkstr = username"
// @Param username string true "账号"
// @Success 200 {json} json {"status": 0, "username": "admin", "mobile": ""}
// @Return status int -2 用户名非法 -1 未注册 0 正常 1 禁用 2 特殊 4 未激活
// @Router /member/status2 [get]
func MemberStatus2(c *gin.Context) {
	username := GetRequestParam(c, "username")
	reason, desp, _ := checkSignature2(c, global.SN_EXPIRE, username, nil)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if username == "" {
		response.FailWithErrcode(response.E_REQUIRE_USERNAME, "", c)
		return
	}
	if strings.Contains(username, "@") && !utils.TestEmail(username) {
		response.OkWithData(gin.H{"status": -2}, c)
		return
	}
	m, err := service.GetMemberByAccount(username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.OkWithData(gin.H{"status": -1}, c)
		} else {
			response.FailWithDetail(response.E_DATABASE, "", gin.H{"status": -1}, c)
		}
		return
	}
	response.OkWithData(gin.H{"status": m.Forbidden, "username": m.Username, "mobile": m.Mobile}, c)
}

// MemberLogin2
// @Param sn string true "签名，checkstr = username + password | access_token （使用 token 登录时）"
// @Param username string false "账号（选填。用户名，手机，邮箱，微信号）"
// @Param password string false "密码（选填）"
// @Success 200 {json} json {"uid": 1, "username": "", "email": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********}
// @Router /member/login2 [get]
func MemberLogin2(c *gin.Context) {
	username := GetRequestParam(c, "username")
	if encrypt, ok := GetAndCheckEncrypt(c); !ok {
		return
	} else if encrypt == 1 {
		username = DecryptParam(c, username)
	}
	password := GetRequestParam(c, "password")
	reason, desp, m := checkSignature2(c, global.SN_EXPIRE, username+password, nil)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if reason == 0 { // 账号，密码登录
		if username == "" {
			response.FailWithErrcode(response.E_REQUIRE_USERNAME, "", c)
			return
		}
		if password == "" {
			response.FailWithErrcode(response.E_REQUIRE_PWD, "", c)
			return
		}
		var err error
		m, err = service.GetMemberByAccount(username)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				response.FailWithErrcode(response.E_UNAMEPWD, "", c)
			} else {
				response.FailWithErrcode(response.E_DATABASE, "", c)
			}
			return
		}
		if reachLimit, err := service.CheckLoginReachLimit(c, m); err != nil {
			c.Error(err)
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		} else if reachLimit {
			response.FailWithErrcode(response.E_LOGIN_LOCK, "", c)
			return
		}
		if !service.CheckPwd(m, password) {
			service.SetTryLogin(c, m, false)
			response.FailWithErrcode(response.E_UNAMEPWD, "", c)
			return
		}
		service.SetTryLogin(c, m, true)
	}

	if !CheckMemberForbidden(c, m) {
		return
	}
	if !IsSubmember(m) {
		if !CheckExampleMachine(c, m.Mobile) {
			return
		}
	}
	if !CheckDeviceLoginMember(c, m) {
		return
	}
	now := time.Now().Unix()
	if now >= m.AccessExpire {
		m.AccessToken = service.CreateMemberAccessToken(m.Uid, now)
	}
	m.AccessExpire = now + global.TOKEN_EXPIRE
	if err := service.SaveMemberAccess(m); err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	appid := GetAppid(c)
	if us, err := service.GetUserStatusByUid(m.Uid); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			us.Uid = int(m.Uid)
			us.Lastip = GetIp(c)
			us.Lastlogin = now
			us.Lastapp = appid
			us.Logincount = 1
			service.CreateUserStatus(us)
		}
	}
	c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
	data := getMemberLoginInfo(m)
	data["email"] = m.Email
	data["mobile"] = m.Mobile
	data["country_code"] = m.CountryCode
	data["access_expire"] = m.AccessExpire
	data["avatar"] = utils.GetAvatarUri(m.Uid)
	if serial := GetDeviceSerial(c); serial != "" {
		data["realname_tips"] = model.RealnameTips
		data["school_tips"] = model.SchoolTips
		real2 := false
		if deviceModel := GetDeviceModel(c); deviceModel != "" && utils.InStringSlice(deviceModel, global.VIPER.GetStringSlice("profile_limit.model")) {
			real2 = true
		}
		if real2 {
			data["realname_tips"] = model.RealnameTips2
		}
		if utils.IsExampleMachine(serial) {
			data["realname_limit"] = 0
			data["school_limit"] = 0
			data["realname_tips"] = ""
			data["school_tips"] = ""
		} else {
			if !real2 {
				data["realname_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitRealname)
			} else {
				data["realname_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitNickname)
			}
			data["school_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitSchool)
		}
	}
	response.OkWithData(data, c)
}

// DeviceMember
// @Param sn string true "签名，checkstr = serial"
// @Success 200 {json} json {"max_member": 5, "list":[{}]}
// @Router /member/device [get]
func DeviceMember(c *gin.Context) {
	serial := GetDeviceSerial(c)
	dModel := GetDeviceModel(c)
	if serial == "" || dModel == "" {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	reason, desp, _ := checkSignature2(c, global.SN_EXPIRE, serial, nil)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, "", gin.H{"reason": reason, "desp": desp}, c)
		return
	}

	data, err := service.GetDeviceMemberList(serial, dModel)
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(data, c)
}

// DeviceMember
// @Param sn string true "签名，checkstr = serial"
// @Param uid int true "uid"
// @Success 200 {json} json {"status": 1}
// @Router /member/unbindDevice [post]
func UnbindDevice(c *gin.Context) {
	serial := GetDeviceSerial(c)
	if serial == "" {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	reason, desp, _ := checkSignature2(c, global.SN_EXPIRE, serial, nil)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	uid, _ := strconv.Atoi(GetRequestParam(c, "uid"))
	if uid <= 0 {
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	err := service.UnbindDeviceMember(serial, uint(uid))
	if err != nil {
		response.FailWithError(err, c)
		return
	}
	response.OkWithData(gin.H{
		"status": 1,
	}, c)
}
