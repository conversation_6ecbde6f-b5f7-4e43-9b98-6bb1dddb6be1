package response

type DeviceMemberRes struct {
	MaxMember int            `json:"max_member"`
	List      []DeviceMember `json:"list"`
}

type DeviceMember struct {
	Uid      uint   `json:"uid"`
	Username string `json:"username"`
	Mobile   string `json:"mobile"`
	Avatar   string `json:"avatar"`
	Realname string `json:"realname"`
	Gender   uint   `json:"gender"`
	Birthday string `json:"birthday"`
	Grade    int    `json:"grade"`
	School   string `json:"school"`
}
