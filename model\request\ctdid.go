package request

// CtdidAuthBizPackage 网络身份认证业务数据
type CtdidAuthBizPackage struct {
	AppName        string `json:"appName" binding:"required,max=32"`    // 应用名称
	Timestamp      string `json:"timestamp" binding:"required,len=17"`  // 时间戳，格式"YYYYMMddHHmmssSSS"
	BizSeq         string `json:"bizSeq" binding:"required,len=32"`     // 业务序列号
	Mode           string `json:"mode" binding:"required,len=3"`        // 认证模式
	CustomerNo     string `json:"customerNo" binding:"required,max=16"` // 业务站点号
	PhotoEncData   string `json:"photoEncData,omitempty"`               // 照片数据（可选）
	CertPwdData    string `json:"certPwdData,omitempty"`                // 网络身份认证凭证口令加密数据（可选）
	IdCardAuthData string `json:"idCardAuthData" binding:"required"`    // 认证请求数据
	ExpectData     string `json:"expectData,omitempty"`                 // 期望返回数据项（可选）
	Cert           string `json:"cert,omitempty"`                       // 加密证书（可选）
}

// ValidateMode 验证认证模式
func (req *CtdidAuthBizPackage) ValidateMode() bool {
	validModes := map[string]bool{
		"R01": true, // 网络身份认证凭证
		"R02": true, // 网络身份认证凭证+口令
		"R03": true, // 网络身份认证凭证+人脸
		"R04": true, // 网络身份认证凭证+口令+人脸
		"R05": true, // 网络身份认证凭证+口令，返年龄标识
		"R06": true, // 网络身份认证凭证+人脸，返年龄标识
		"R07": true, // 网络身份认证凭证+口令+人脸，返年龄标识
	}
	return validModes[req.Mode]
}

// RequiresPhoto 检查是否需要人脸数据
func (req *CtdidAuthBizPackage) RequiresPhoto() bool {
	return req.Mode == "R03" || req.Mode == "R04" || req.Mode == "R06" || req.Mode == "R07"
}

// RequiresPassword 检查是否需要口令数据
func (req *CtdidAuthBizPackage) RequiresPassword() bool {
	return req.Mode == "R02" || req.Mode == "R04" || req.Mode == "R05" || req.Mode == "R07"
}

// RequiresAgeInfo 检查是否需要返回年龄标识
func (req *CtdidAuthBizPackage) RequiresAgeInfo() bool {
	return req.Mode == "R05" || req.Mode == "R06" || req.Mode == "R07"
}

// RequiresCert 检查是否需要加密证书
func (req *CtdidAuthBizPackage) RequiresCert() bool {
	return req.RequiresAgeInfo()
}
