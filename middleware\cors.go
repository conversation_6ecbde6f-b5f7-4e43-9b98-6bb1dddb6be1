package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Cors 处理跨域请求,支持options访问
func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		origin := c.Request.Header.Get("Origin")
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
		c.<PERSON>("Access-Control-Allow-Headers", "Content-Type,AccessToken,Authorization,X-CSRF-Token,Token, Accept,User-Agent,Referer,Connection,Content-Length,Accept-Encoding,Origin,Accept-Language")
		c.<PERSON>er("Access-Control-Allow-Methods", "POST, GET, OPTIONS,DELETE,PUT")
		c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
		c.<PERSON>("Access-Control-Allow-Credentials", "true")

		// 放行所有OPTIONS方法
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
		}
		// 处理请求
		c.Next()
	}
}
