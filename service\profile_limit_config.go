package service

import (
	"account/global"
	"account/model"
	"net"
	"strings"
)

// 检查是否启用限制
func CheckProfileLimitConfig(configType int, ip string) (ok bool) {
	var m model.ProfileLimitConfig
	global.DB.Model(&model.ProfileLimitConfig{}).Where("`type` = ?", configType).Take(&m)
	if m.ID == 0 || m.Status == 0 {
		return false
	}
	switch m.Ip {
	case "", "*":
		return true
	default:
		ipn := net.ParseIP(ip)
		for _, v := range strings.Split(m.Ip, ",") {
			if !strings.Contains(v, "/") {
				v += "/32"
			}
			if _, ipNet, err1 := net.ParseCIDR(v); err1 == nil {
				if ipNet.Contains(ipn) {
					return true
				}
			}
		}
		return false
	}
}
