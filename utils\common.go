package utils

import (
	"account/global"
	"encoding/hex"
	"strconv"

	uuid "github.com/satori/go.uuid"
)

// GetAvatarUri 获取用户头像图片链接
func GetAvatarUri(uid uint) string {
	return global.CONFIG.App.AvatarHost + "/" + global.CONFIG.App.AvatarDir + "/" + strconv.Itoa(int(uid)) + "@!s"
}

// MaskMobile 使用*遮掩部分手机号
func MaskMobile(mobile string) string {
	if mobile == "" {
		return ""
	}
	n := len(mobile)
	prefix := 1
	suffix := ""
	if (n-4)/2 > 0 {
		prefix = (n - 4) / 2
	}
	if n-4 > prefix {
		suffix = mobile[prefix+4:]
	}
	return mobile[:prefix] + "****" + suffix
}

// Uniqid 取 uuidV4 的前7bytes作为唯一id标识
func Uniqid() string {
	return hex.EncodeToString(uuid.NewV4().Bytes()[:7])
}

func InStringSlice(target string, slice []string) bool {
	for _, v := range slice {
		if v == target {
			return true
		}
	}
	return false
}
