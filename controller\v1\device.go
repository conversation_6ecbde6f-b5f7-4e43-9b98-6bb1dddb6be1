package v1

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/service"
	"time"

	"github.com/gin-gonic/gin"
)

// DeviceLogin 设备登录
func DeviceLogin(c *gin.Context) {
	response.OkWithData(gin.H{"success": true}, c)
}

// DeviceAdd 增加设备【用于穿戴设备】
// @Param model string true "机型"
// @Param uniqno string true "序列号"
// @Param buytime time true "购买时间（2006-01-02 15:04:05）"
// @Param origin string true "手机号"
// @Success {json} json {"model": "", "uniqno": "", "origin": "", "buytime": "", "status": true}
// @Router /device/add [get]
func DeviceAdd(c *gin.Context) {
	model := GetRequestParam(c, "model")
	uniqno := GetRequestParam(c, "uniqno")
	origin := GetRequestParam(c, "origin")
	buytime := GetRequestParam(c, "buytime")

	status := true
	if uniqno == "" || model == "" || origin == "" {
		status = false
	} else {
		c.Set("uinfo", gin.H{"uid": 0, "mobile": origin})
		c.Set("dinfo", gin.H{
			"model": model, "serial": uniqno, "uniqno": uniqno, "buytime": buytime, "origin": origin,
			"release": "", "version": "", "did": "", "mac": "",
		})
	}
	response.OkWithData(gin.H{
		"model": model, "uniqno": uniqno, "buytime": buytime, "origin": origin,
		"status": status,
	}, c)
}

// DeviceCheck 设备激活检查
// login yes
// @Param sn string true "签名"
// @Success {json} json {"success": true}
// @Router /device/check [get]
func DeviceCheck(c *gin.Context) {
	m, ok := device_checkSN(c)
	if !ok {
		return
	}
	var success bool
	if m.Forbidden == 0 && m.AccessExpire > time.Now().Unix() {
		success = true
	}

	m.AccessExpire = time.Now().Unix() + global.TOKEN_EXPIRE
	if err := service.SaveMemberAccess(m); err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
	response.OkWithData(gin.H{"status": 1, "success": success}, c)
}

func device_checkSN(c *gin.Context) (mem *model.AcMember, ok bool) {
	reason, desc, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desc, gin.H{"status": 1}, c)
		return
	}
	if reason == 0 || m == nil || m.Uid == 0 {
		response.FailWithDetail(response.E_NOT_LOGIN, "", gin.H{"status": 1}, c)
		return
	}
	return m, true
}
