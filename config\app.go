package config

type App struct {
	ServerName    string `mapstructure:"server-name"`
	ServerVersion string `mapstructure:"server-version"`
	LastModified  int64  `mapstructure:"last-modified"`
	Domain        string `mapstructure:"domain"`
	AvatarHost    string `mapstructure:"avatar-host"`
	AvatarDir     string `mapstructure:"avatar-dir"`
	Env           string `mapstructure:"env"`
	Port          int    `mapstructure:"port"`
	EnableMQ      bool   `mapstructure:"enable-mq"`
	AppSecPath    string `mapstructure:"appsec-path"`
}
