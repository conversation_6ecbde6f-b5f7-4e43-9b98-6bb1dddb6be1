package model

import "time"

type UserProfileExtra struct {
	Uid       uint   `gorm:"not null;index:uk_uid,unique"`
	CardNo    string `gorm:"not null;type:varchar(128);comment:身份证号;"`
	Username  string `gorm:"not null;type:varchar(128);comment:真实姓名"`
	Gender    uint   `gorm:"not null;type:tinyint(3);comment:性别。0-男；1-女"`
	School    string `gorm:"not null;type:varchar(128);comment:学校信息"`
	RawData   string `gorm:"type:text;comment:原始数据"` // TODO 考虑是否加密、去除敏感信息等；不同平台数据如何存储
	CreatedAt time.Time
}

func (UserProfileExtra) TableName() string {
	return "user_profile_extra"
}
