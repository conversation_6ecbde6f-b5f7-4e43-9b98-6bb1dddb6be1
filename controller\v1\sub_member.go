package v1

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

const (
	SUBMEM_LIMIT         = 4 // 普通用户子账号数量
	SUBMEM_LIMIT_EXAMPLE = 4 // 样机子账号数量
)

func IsSubmember(m *model.AcMember) bool {
	if m == nil || !strings.HasPrefix(m.Username, "sub-") {
		return false
	}
	return strings.HasPrefix(m.Username, "sub-")

	// TODO 若子账号可变为主账号，使用下方逻辑
	puid, _ := service.GetPrimaryUid(m.Uid)
	return puid != m.Uid
}

func submember_checkSn(c *gin.Context) (m, pm *model.AcMember, ok bool) {
	m, ok = CheckLogin(c)
	if !ok {
		return
	}
	puid := GetRequestParam(c, "puid")
	if puid == "" {
		ok = false
		response.FailWithErrcode(response.E_PARAM, "", c)
		return
	}
	pid, _ := strconv.Atoi(puid)
	if !IsSubmember(m) && pid == int(m.Uid) {
		pm = m
		ok = true
		return
	}
	exists, err := service.ExistsSubmember(c, uint(pid), m.Uid)
	if err != nil {
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	if !exists {
		response.Fail(response.E_MEMBER_NOT_EXISTS, "错误的主账号", c)
		return
	}
	ok = true
	pm, _ = service.GetMemberByUid(puid)
	c.Set("uinfo", nil) // 减少消息发送数量
	return
}

func getSubmemLimit(c *gin.Context) (limit int) {
	limit = SUBMEM_LIMIT
	return
	if serial := GetDeviceSerial(c); serial != "" && utils.IsExampleMachine(serial) {
		limit = SUBMEM_LIMIT_EXAMPLE
	}
	return
}

type submAdd struct {
	Realname   string `binding:"omitempty,min=2,max=60" json:"realname" form:"realname"`
	Gender     uint   `binding:"omitempty,oneof=0 1" json:"gender" form:"gender"`
	Birthday   string `json:"birthday" form:"birthday"`
	Grade      int    `json:"grade" form:"grade"`
	School     string `binding:"omitempty,min=2,max=100" json:"school" form:"school"`
	ProvinceId int    `form:"province_id" json:"province_id"`
	CityId     int    `form:"city_id" json:"city_id"`
	DistrictId int    `form:"district_id" json:"district_id"`
}

func (submAdd) GetError(err validator.ValidationErrors) string {
	if len(err) == 0 {
		return "参数错误"
	}
	val := err[0]
	switch val.Field() {
	case "Realname":
		return "昵称须为2-60个字符"
	case "Gender":
		return "性别须为0或1"
	case "School":
		return "学校须为2-100个字符"
	case "Grade":
		return "年级须为1-3位数字"
	}
	return "参数错误"
}

type subMemResp struct {
	Uid          uint   `json:"uid"`
	Suid         uint   `json:"suid"`
	Username     string `json:"username"`
	Mobile       string `json:"mobile"`
	Avatar       string `json:"avatar"`
	AccessToken  string `json:"access_token,omitempty"`
	AccessExpire int64  `json:"access_expire,omitempty"`
	submAdd
}

// SubmemberAdd 添加子账号
// login yes
// @Param sn string true "主账号签名"
// @Param realname string true "昵称"
// @Param gender int true "性别"
// @Param birthday string true "生日，格式：Y-m-d"
// @Param grade int true "年级"
// @Param school string true "学校"
// @Success 200 {json} json
// @Router /Submember/add [post]
func SubmemberAdd(c *gin.Context) {
	_, pm, ok := submember_checkSn(c)
	if !ok {
		return
	}
	var req submAdd
	if err := c.ShouldBind(&req); err != nil {
		if verr, ok := err.(validator.ValidationErrors); ok {
			response.Fail(response.E_PARAM, req.GetError(verr), c)
		} else {
			response.Fail(response.E_PARAM, "", c)
		}
		return
	}
	var (
		birth_y int
		birth_m time.Month
		birth_d int
	)
	if req.Birthday != "" {
		birthday, err := time.Parse(global.TIME_LAYOUT_DATE, req.Birthday)
		if err != nil {
			response.Fail(response.E_PARAM, "生日格式错误", c)
			return
		}
		birth_y, birth_m, birth_d = birthday.Date()
	}
	limit := getSubmemLimit(c)
	tx := global.DB.Begin()
	slist, err := service.GetSubmembersByUidWithTx(tx, pm.Uid)
	if err != nil {
		tx.Rollback()
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	if len(slist) >= limit {
		tx.Commit()
		response.Fail(response.E_UNKNOWN, "子账号数量达到上限", c)
		return
	}

	// 创建子账号映射
	subm := model.AcSubmember{
		Uid:  pm.Uid,
		Sort: len(slist) + 1,
	}
	if err := service.CreateSubmemberWithTx(tx, &subm); err != nil {
		tx.Rollback()
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	// 创建子账号
	now := time.Now().Unix()
	appid := GetAppid(c)
	m := model.AcMember{
		Username:  "sub-" + strconv.Itoa(int(subm.ID)),
		Regip:     GetIp(c),
		Regdate:   now,
		Regapp:    appid,
		Forbidden: 0,
	}
	m.AccessToken = utils.MD5(m.Username + strconv.FormatInt(now, 10))
	m.AccessExpire = now + int64(global.TOKEN_EXPIRE)
	if err := service.CreateMember(&m); err != nil {
		tx.Rollback()
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	// 回填子账号 uid
	if service.UpdateSubmemberWithTx(tx, subm.ID, map[string]interface{}{"suid": m.Uid}) != nil {
		tx.Rollback()
		service.DeleteMemberByUid(m.Uid)
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	tx.Commit()

	p := model.UserProfile{
		Uid:        int(m.Uid),
		Username:   m.Username,
		Realname:   req.Realname,
		School:     req.School,
		Gender:     req.Gender,
		Grade:      req.Grade,
		BirthY:     birth_y,
		BirthM:     int(birth_m),
		BirthD:     birth_d,
		ProvinceId: uint(req.ProvinceId),
		CityId:     uint(req.CityId),
		DistrictId: uint(req.DistrictId),
	}
	if req.ProvinceId == 0 && req.CityId == 0 {
		pp, _ := service.GetProfileByUid(pm.Uid)
		if pp != nil {
			p.ProvinceId = pp.ProvinceId
			p.CityId = pp.CityId
			p.DistrictId = pp.DistrictId
		}
	}
	service.CreateProfile(&p)
	data := subMemResp{
		Uid:          pm.Uid,
		Suid:         m.Uid,
		Username:     m.Username,
		Mobile:       pm.Mobile,
		AccessToken:  m.AccessToken,
		AccessExpire: m.AccessExpire,
		submAdd:      req,
	}
	if uploadSubmemberAvatar(c, int(m.Uid)) == nil {
		data.Avatar = utils.GetAvatarUri(m.Uid)
	}
	response.OkWithData(data, c)
}

func uploadSubmemberAvatar(c *gin.Context, suid int) (err error) {
	avatar, err := c.FormFile("avatar")
	if err != nil {
		avatar, err = c.FormFile("image")
	}
	if err != nil {
		return
	}
	if avatar.Size > 2097152 || avatar.Size == 0 {
		err = errors.New("文件大小错误")
		return
	}
	mimetype := avatar.Header.Get("Content-Type")
	if mimetype != "image/png" && mimetype != "image/jpeg" && mimetype != "image/pjpeg" {
		err = errors.New("文件格式错误")
		return
	}
	dst := global.CONFIG.App.AvatarDir + "/" + strconv.Itoa(suid)
	fd, err := avatar.Open()
	if err != nil {
		err = errors.New("临时文件打开失败")
		return
	}
	defer fd.Close()
	err = utils.UploadFile(fd, dst)
	if err != nil {
		err = errors.New("文件上传失败")
	}
	return
}

// SubmemberList 获取子账号列表
// login yes
// @Param sn string true "主账号签名"
// @Success 200 {json} json
// @Router /Submember/list [get]
func SubmemberList(c *gin.Context) {
	_, pm, ok := submember_checkSn(c)
	if !ok {
		return
	}
	slist, err := service.GetSubmembersByUid(pm.Uid)
	if err != nil {
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	if len(slist) == 0 {
		m := model.AcSubmember{
			ID:   0,
			Suid: pm.Uid,
			Uid:  pm.Uid,
			Sort: 0,
		}
		err := service.CreateSubmember(&m)
		if err != nil {
			response.Fail(response.E_DATABASE, "", c)
			return
		}
		slist = append(slist, m)
	}
	suids := make([]uint, 0, len(slist))
	for i := range slist {
		suids = append(suids, slist[i].Suid)
	}
	pMap, err := service.GetProfileMapByUids(suids, "uid,username,realname,birth_y,birth_m,birth_d,gender,grade,school")
	if err != nil {
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	data := make([]subMemResp, 0, len(suids))
	for _, uid := range suids {
		if p, ok := pMap[uid]; ok {
			subm := subMemResp{
				Uid:      pm.Uid,
				Suid:     uid,
				Username: p.Username,
				Mobile:   pm.Mobile,
				Avatar:   utils.GetAvatarUri(uid),
				submAdd: submAdd{
					Realname: p.Realname,
					Gender:   p.Gender,
					Grade:    p.Grade,
					School:   p.School,
				},
			}
			if p.BirthY > 0 && p.BirthM > 0 && p.BirthD > 0 {
				subm.Birthday = fmt.Sprintf("%d-%02d-%02d", p.BirthY, p.BirthM, p.BirthD)
			}
			data = append(data, subm)
		}
	}
	limit := getSubmemLimit(c)
	response.OkWithData(gin.H{
		"result": data,
		"limit":  limit,
	}, c)
}

// SubmemberLogin 获取子账号token
// login yes
// @Param sn string true "主账号签名"
// @Param suid int  true "子账号uid"
// @Success 200 {json} json {"status": "success"}
// @Router /Submember/login [get]
func SubmemberLogin(c *gin.Context) {
	_, pm, ok := submember_checkSn(c)
	if !ok {
		return
	}
	suidstr := GetRequestParam(c, "suid")
	suid, _ := strconv.ParseInt(suidstr, 10, 64)
	if suid == 0 {
		response.Fail(response.E_PARAM, "子账号uid不能为空", c)
		return
	}
	var m *model.AcMember
	if suid != int64(pm.Uid) {
		exists, err := service.ExistsSubmember(c, pm.Uid, uint(suid))
		if err != nil {
			response.Fail(response.E_DATABASE, "", c)
			return
		}
		if !exists {
			response.Fail(response.E_MEMBER_NOT_EXISTS, "", c)
			return
		}
		m, _ = service.GetMemberByUid(suidstr)
		if m == nil || m.Uid == 0 {
			response.Fail(response.E_MEMBER_NOT_EXISTS, "", c)
			return
		}
	} else {
		m = pm
	}
	if !CheckDeviceLoginMember(c, m) {
		return
	}
	p, err := service.GetProfileByUid(m.Uid)
	if err != nil {
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	if now := time.Now().Unix(); now > m.AccessExpire {
		m.AccessToken = utils.MD5(m.Username + strconv.FormatInt(now, 10))
		m.AccessExpire = now + int64(global.TOKEN_EXPIRE)
		service.SaveMemberAccess(m)
	}
	data := getMemberLoginInfo(m)
	data["email"] = m.Email
	data["mobile"] = m.Mobile
	data["country_code"] = m.CountryCode
	data["access_expire"] = m.AccessExpire
	data["avatar"] = utils.GetAvatarUri(m.Uid)
	data["realname_tips"] = model.RealnameTips
	data["school_tips"] = model.SchoolTips
	real2 := false
	if deviceModel := GetDeviceModel(c); deviceModel != "" && utils.InStringSlice(deviceModel, global.VIPER.GetStringSlice("profile_limit.model")) {
		real2 = true
	}
	if real2 {
		data["realname_tips"] = model.RealnameTips2
	}
	serial := GetDeviceSerial(c)
	if serial != "" && utils.IsExampleMachine(serial) {
		data["realname_limit"] = 0
		data["school_limit"] = 0
		data["realname_tips"] = ""
		data["school_tips"] = ""
	} else {
		if !real2 {
			data["realname_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitRealname)
		} else {
			data["realname_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitNickname)
		}
		data["school_limit"] = service.CheckProfileLimit(int(m.Uid), model.ProfileLimitSchool)
	}
	data["uid"] = pm.Uid
	data["suid"] = m.Uid
	data["realname"] = p.Realname
	data["gender"] = p.Gender
	data["birthday"] = fmt.Sprintf("%d-%02d-%02d", p.BirthY, p.BirthM, p.BirthD)
	data["grade"] = p.Grade
	data["school"] = p.School
	response.OkWithData(data, c)
}

// SubmemberRemove 移除子账号
// login yes
// @Param sn string true "主账号签名"
// @Param suid int  true "子账号uid"
// @Success 200 {json} json {"status": "success"}
// @Router /Submember/remove [get]
func SubmemberRemove(c *gin.Context) {
	_, pm, ok := submember_checkSn(c)
	if !ok {
		return
	}
	suidstr := GetRequestParam(c, "suid")
	suid, _ := strconv.ParseInt(suidstr, 10, 64)
	if suid == 0 {
		response.Fail(response.E_PARAM, "子账号uid不能为空", c)
		return
	}
	if suid == int64(pm.Uid) {
		response.OkWithData(gin.H{"status": "success"}, c)
		return
	}
	subm, err := service.GetSubmembersBySuid(pm.Uid, uint(suid))
	if err != nil || subm.ID == 0 {
		response.OkWithData(gin.H{"status": "success"}, c)
		return
	}
	err = service.DeleteSubmemberByID(subm.ID)
	if err != nil {
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	// TODO 考虑子账号绑定手机或邮箱后，是否可以作为独立账号（即仅解绑与主账号的关联，成为新的主账号）
	service.DeleteProfileByUid(subm.Suid)
	if err := service.DeleteMemberByUid(subm.Suid); err != nil {
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	response.OkWithData(gin.H{"status": "success"}, c)
}

// SubmemberReorder 子账号重新排序
// login yes
// @Param sn string true "主账号签名"
// @Param uids string  true "子账号uid数组"
// @Success 200 {json} json {"status": "success"}
// @Router /Submember/reorder [get]
func SubmemberReorder(c *gin.Context) {
	_, pm, ok := submember_checkSn(c)
	if !ok {
		return
	}
	suids := GetRequestParam(c, "uids")
	uids := utils.SplitInt64str(suids, ",")
	if len(uids) == 0 {
		response.OkWithData(gin.H{"status": "success"}, c)
		return
	}
	err := service.SubmemberReorder(pm.Uid, uids)
	if err != nil {
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	response.OkWithData(gin.H{"status": "success"}, c)
}

// TODO 修改api权限策略
var sub_simpleListApps = map[string]struct{}{
	"aide.readboy.com":       {},
	"api1-yx.readboy.com":    {},
	"study-plan.readboy.com": {},
}

type subSimpleListResp struct {
	Uid      uint   `json:"uid"`
	Suid     uint   `json:"suid"`
	Gender   uint   `json:"gender"`
	Grade    int    `json:"grade"`
	Nickname string `json:"nickname"`
	Birthday string `json:"birthday"`
	School   string `json:"school"`
	Avatar   string `json:"avatar"`
	Username string `json:"username"`
}

// 通过uid获取子账号列表，限定使用
func SubmemberSimpleList(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"status": reason}, c)
		return
	}
	appid := GetAppid(c)
	if _, ok := sub_simpleListApps[appid]; !ok {
		response.FailWithErrcode(response.E_ACCESS_FORBIDDEN, "", c)
		return
	}
	uidstr := c.Query("uid")
	uid, _ := strconv.Atoi(uidstr)
	if uid <= 0 {
		response.Fail(response.E_PARAM, "uid不能为空", c)
		return
	}

	puid, _ := service.GetPrimaryUid(uint(uid))
	suids := make([]uint, 0, 4)
	if puid > 0 {
		slist, err := service.GetSubmembersByUid(puid)
		if err != nil {
			response.Fail(response.E_DATABASE, "", c)
			return
		}
		for i := range slist {
			suids = append(suids, slist[i].Suid)
		}
	} else {
		suids = append(suids, uint(uid))
		puid = uint(uid)
	}
	pMap, err := service.GetProfileMapByUids(suids, "uid,username,realname,birth_y,birth_m,birth_d,gender,grade,school")
	if err != nil {
		response.Fail(response.E_DATABASE, "", c)
		return
	}
	data := make([]subSimpleListResp, 0, len(suids))
	for _, uid := range suids {
		if p, ok := pMap[uid]; ok {
			subm := subSimpleListResp{
				Uid:      puid,
				Suid:     uid,
				Gender:   p.Gender,
				Grade:    p.Grade,
				Nickname: p.Realname,
				School:   p.School,
				Avatar:   utils.GetAvatarUri(uint(p.Uid)),
				Username: pMap[puid].Username,
			}
			if p.BirthY > 0 && p.BirthM > 0 && p.BirthD > 0 {
				subm.Birthday = fmt.Sprintf("%d-%02d-%02d", p.BirthY, p.BirthM, p.BirthD)
			}
			data = append(data, subm)
		}
	}
	response.OkWithData(gin.H{
		"result": data,
	}, c)
}
