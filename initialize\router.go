package initialize

import (
	v1 "account/controller/v1"
	"account/global"
	"account/middleware"
	"account/model/response"
	"account/router"
	"account/utils"
	"io"
	"os"

	"github.com/gin-gonic/gin"
)

func Routers() *gin.Engine {
	if global.CONFIG.App.Env == "public" {
		gin.SetMode(gin.ReleaseMode)
	}
	var Router = gin.New()
	var w io.Writer = os.Stdout
	if ws, err := utils.GetWriteSyncer(); err == nil {
		w = ws
	}
	gin.DefaultWriter = w
	Router.Use(middleware.Logger(), gin.RecoveryWithWriter(w), middleware.Cors())

	Router.NoRoute(v1.RouteNoFound)

	SystemRouter := Router.Group("")
	{
		SystemRouter.Any("", v1.ServerInfo)
	}

	ApiRouter := Router.Group("")
	ApiRouter.GET("device/check", middleware.DeviceCheckMiddleware(), v1.<PERSON><PERSON><PERSON>heck)

	ApiRouter.Use(middleware.ControllerBase())
	{
		router.InitActivateRouter(ApiRouter)
		router.InitBadwordsRouter(ApiRouter)
		router.InitBeanRouter(ApiRouter)
		router.InitBillRouter(ApiRouter)
		router.InitCtdidRouter(ApiRouter)
		router.InitDeviceRouter(ApiRouter)
		router.InitEbagRouter(ApiRouter)
		router.InitEmailRouter(ApiRouter)
		router.InitLinkRouter(ApiRouter)
		router.InitLocationRouter(ApiRouter)
		router.InitMemberRouter(ApiRouter)
		router.InitMobileRouter(ApiRouter)

		router.InitNativeRouter(ApiRouter)
		router.InitProfileRouter(ApiRouter)
		router.InitPutRouter(ApiRouter)
		router.InitResourceRouter(ApiRouter)
		router.InitSearchRouter(ApiRouter)
		router.InitServiceRouter(ApiRouter)
		router.InitTokenRouter(ApiRouter)
		router.InitUploadRouter(ApiRouter)
		router.InitWxRouter(ApiRouter)
		router.InitSubmemberRouter(ApiRouter)
		router.InitWearRouter(ApiRouter)
		router.InitScanpenRouter(ApiRouter)
		router.InitZtRouter(ApiRouter)
		router.InitFaceRouter(ApiRouter)
	}

	return Router
}

func debug(r *gin.RouterGroup) {
	debugR := r.Group("debug")
	{
		debugR.POST("", func(c *gin.Context) {
			response.OkWithData(gin.H{"success": true}, c)
		})
	}
}
