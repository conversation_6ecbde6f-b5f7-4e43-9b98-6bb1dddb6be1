package service

import (
	"account/global"
	"account/model"
	"account/pkg/apis/shumei"
	"account/utils"
	"encoding/base64"
	"strconv"
	"time"

	"github.com/go-redis/redis"
)

func CreateShumeiLog(m *model.ShumeiLog) error {
	return global.DB.Table("shumei_log").Create(m).Error
}

func ShumeiTextCheck(text string) (pass bool, err error) {
	key := "account:shumei:textPass"
	key2 := "account:shumei:textFail" // 最近10分钟内失败的
	score, _ := global.Redis.ZScore(key, text).Result()
	dateScore := time.Now().AddDate(0, 0, -7).Unix()
	if score > 0 && score > float64(dateScore) {
		return true, nil
	}
	score2, _ := global.Redis.ZScore(key2, text).Result()
	failScore := time.Now().Add(-10 * time.Minute).Unix()
	if score2 > 0 && score2 > float64(failScore) {
		return false, nil
	}
	checkRes, err := shumei.TextCheck(&shumei.TextCheckReq{
		Data: shumei.TextData{
			Text:     text,
			Nickname: text,
		},
	})
	if err != nil {
		return
	}
	if pass = checkRes.IsPass(); pass {
		global.Redis.ZAdd(key, redis.Z{
			Score:  float64(time.Now().Unix()),
			Member: text,
		})
		global.Redis.ZRemRangeByScore(key, "0", strconv.FormatInt(dateScore, 10))
	} else {
		global.Redis.ZAdd(key2, redis.Z{
			Score:  float64(time.Now().Unix()),
			Member: text,
		})
		global.Redis.ZRemRangeByScore(key2, "0", strconv.FormatInt(failScore, 10))
	}
	return
}

func ShumeiImgCheck(uid uint, img []byte) (pass bool, err error) {
	member := utils.MD5Bytes(img)
	key := "account:shumei:imgPass"  // 最近7天通过的
	key2 := "account:shumei:imgFail" // 最近1小时内失败的
	score, _ := global.Redis.ZScore(key, member).Result()
	dateScore := time.Now().AddDate(0, 0, -7).Unix()
	if score > 0 && score > float64(dateScore) {
		return true, nil
	}
	score2, _ := global.Redis.ZScore(key2, member).Result()
	failScore := time.Now().Add(-time.Hour).Unix()
	if score2 > 0 && score2 > float64(failScore) {
		return false, nil
	}

	base64Str := base64.StdEncoding.EncodeToString(img)
	checkRes, err := shumei.ImageCheck(&shumei.ImageCheckReq{
		Uid: int(uid),
		Data: shumei.ImageData{
			Img: base64Str,
		},
	})
	if err != nil {
		return
	}
	if pass = checkRes.IsPass(); pass {
		global.Redis.ZAdd(key, redis.Z{
			Score:  float64(time.Now().Unix()),
			Member: member,
		})
		global.Redis.ZRemRangeByScore(key, "0", strconv.FormatInt(dateScore, 10))
	} else {
		global.Redis.ZAdd(key2, redis.Z{
			Score:  float64(time.Now().Unix()),
			Member: member,
		})
		global.Redis.ZRemRangeByScore(key2, "0", strconv.FormatInt(failScore, 10))
	}
	return
}
