package utils

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"regexp"
	"strings"
)

func MD5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

func HmacSha256(str string) string {
	h := hmac.New(sha256.New, []byte("RTd3h3bsaafk6XYo2cctPxtjN"))
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

func Base64Decode(str string) ([]byte, error) {
	replacer := strings.NewReplacer(
		"\r", "",
		"\n", "",
		" ", "+",
		"-", "+",
		"_", "/",
	)
	str = replacer.Replace(str)
	r := regexp.MustCompile(`data:([^;]*);base64,(.*)`)
	ss := r.FindAllStringSubmatch(str, -1)
	base64Str := ""
	if len(ss) > 0 && len(ss[0]) > 2 {
		base64Str = ss[0][2]
	}
	return base64.StdEncoding.DecodeString(base64Str)
}

func MD5Bytes(data []byte) string {
	h := md5.New()
	h.Write(data)
	return hex.EncodeToString(h.Sum(nil))
}
