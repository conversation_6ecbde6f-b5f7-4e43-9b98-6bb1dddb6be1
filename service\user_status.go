package service

import (
	"account/global"
	"account/model"
	"errors"
	"strings"

	"gorm.io/gorm"
)

func GetUserStatusByUid(uid uint) (userStatus *model.UserStatus, err error) {
	var us model.UserStatus
	err = global.DB.Model(&model.UserStatus{}).First(&us, "uid=?", uid).Error
	userStatus = &us
	return
}

func CreateUserStatus(us *model.UserStatus) error {
	db := global.DB.Model(&model.UserStatus{})
	return db.Create(&us).Error
}

func GetUserStatusByUids(uids string) (list []model.UserStatus, err error) {
	ids := strings.Split(uids, ",")
	err = global.DB.Model(&model.UserStatus{}).Find(&list, "uid in (?)", ids).Error
	return
}

func SaveUserStatus(us *model.UserStatus) error {
	return global.DB.Model(&model.UserStatus{}).Where("uid=?", us.Uid).Limit(1).Save(us).Error
}

// CreateOrUpdateUserStatus 创建或更新用户登录记录
func CreateOrUpdateUserStatus(uid uint, ip, appid string, timestamp int64) (s *model.UserStatus, err error) {
	if s, err := GetUserStatusByUid(uid); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			s.Uid = int(uid)
			s.Lastip = ip
			s.Lastlogin = timestamp
			s.Lastapp = appid
			s.Logincount = 1
			CreateUserStatus(s)
		} else {
			return nil, err
		}
	} else {
		s.Lastip = ip
		s.Lastlogin = timestamp
		s.Lastapp = appid
		s.Logincount += 1
		SaveUserStatus(s)
	}
	return s, nil
}
