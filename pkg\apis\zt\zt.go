package zt

import (
	"account/pkg/apis"
	"fmt"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/viper"
)

var client = resty.New().SetDebug(true).SetDebugBodyLimit(1e5)
var cfg apis.Config

func SetConfig(v *viper.Viper) {
	var c apis.Config
	if v.Unmarshal(&c) != nil {
		return
	}
	if c.Host != "" && c.Host != cfg.Host {
		cfg.Host = c.Host
		client.SetBaseURL(cfg.Host)
	}
	if c.Appid != "" && c.Appid != cfg.Appid {
		cfg.Appid = c.Appid
	}
	if c.Appsec != "" && c.Appsec != cfg.Appsec {
		cfg.Appsec = c.Appsec
	}
}

type Resp struct {
	Code    int         `json:"code"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data"`
}

func checkResp(resp *Resp) (err error) {
	if resp.Code != 100200 {
		err = fmt.Errorf("错误（%d）：%s", resp.Code, resp.Message)
		return
	}
	return nil
}
