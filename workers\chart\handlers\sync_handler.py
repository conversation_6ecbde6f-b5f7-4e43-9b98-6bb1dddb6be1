import json
import requests
from logger import log
import time
from handlers.handler import Handler

class SyncHandler(Handler):
    """
        处理设备位置信息，并提交到：http://statistics.readboy.com/api/i/loc
    """
    
    def handle(self, message_id, message_tag, data):
        try:
            device = data['device'] if 'device' in data else None
            user = data['user'] if 'user' in data else None
            
            if device != None and 'id' in device and 'location' in device:
                if ',' in device['location']:
                    '''post device location'''
                    lng, lat = device['location'].split(',')
                    log('LocationHandler:', 'Post location: ', device['id'], lng, lat)
                    url = 'http://statistics.readboy.com/api/i/loc'
                    res = requests.request('POST', url, data={'id': device['id'], 't': int(time.time()), 'lng': lng, 'lat': lat, 'model': device['model']})
                    #log(json.loads(res.text))
        except Exception as e:
            log('LocationHandler.handle error:', e, type(e))
