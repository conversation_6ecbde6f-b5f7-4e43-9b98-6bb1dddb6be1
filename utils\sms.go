package utils

import (
	"account/global"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/url"
	"time"

	"go.uber.org/zap"
)

const (
	appname = "account.readboy.com"
	smshost = "https://api-sms.readboy.com"
	smsUrl  = "https://api-sms.readboy.com/index.php?s=/Sms/Api/send"
	smsKey  = "8e517d999976168979f81dc73d010c90"
)

type SendSMSResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// SendSMS 发送短信
//
// @Param mobile 手机号
//
// @Param tplCode 模板编号
//
// @Param tplParam 模板内容键值对（json字符串），如：{"code": "123456"}
//
// @Param phoneCode 国际区号，不含+
func SendSMS(mobile, tplCode, tplParam, phoneCode string) (result SendSMSResponse, ok bool) {
	key := smsKey
	if mobile == "" || tplCode == "" || key == "" {
		return
	}
	now := time.Now().Unix()
	code := rand.Intn(900000) + 100000
	authKey := fmt.Sprintf("%d-%d-%s", now, code, MD5(fmt.Sprintf("%d-%d-%s", now, code, key)))
	query := url.Values{}
	query.Set("authKey", authKey)
	query.Set("appName", appname)
	query.Set("version", "1.0")
	query.Set("model", "")
	query.Set("signName", "读书郎")
	query.Set("templateCode", tplCode)
	query.Set("phoneNumber", mobile)
	query.Set("templateParam", tplParam)
	query.Set("phoneCode", phoneCode)

	resp, err := SendPostForm(smsUrl, query)
	if err != nil {
		result.Code = "-1"
		global.LOG.Error("send sms error, mobile: "+mobile, zap.Error(err))
		return
	}
	if err = json.Unmarshal(resp, &result); err != nil {
		result.Code = "-1"
		global.LOG.Error("mobile: "+mobile, zap.Error(err))
		return
	}
	if result.Code != "200" {
		global.LOG.Error("mobile: "+mobile, zap.String("message", result.Message))
		return
	}
	ok = true
	return
}

func SendSMSVerify(mobile, verify string, phoneCode string, signName ...string) (SendSMSResponse, bool) {
	tplCode := "SMS_139239773"
	tplParam := `{"code": "` + verify + `"}`
	code := "86"
	if phoneCode != "" {
		code = phoneCode
	}
	if len(signName) > 0 && signName[0] != "" {
		tplCode = "study_room_tablet_code"
		return SendSMS3t(mobile, tplCode, tplParam, code, signName[0])
	}
	return SendSMS(mobile, tplCode, tplParam, code)
}

// SendSMS3t 发送短信
func SendSMS3t(mobile, tplCode, tplParam, phoneCode string, signName string) (result SendSMSResponse, ok bool) {
	key := smsKey
	if mobile == "" || tplCode == "" || key == "" {
		return
	}
	signName = "读书郎" // 暂时用回读书郎的签名 TODO
	now := time.Now().Unix()
	code := rand.Intn(900000) + 100000
	authKey := fmt.Sprintf("%d-%d-%s", now, code, MD5(fmt.Sprintf("%d-%d-%s", now, code, key)))
	query := url.Values{}
	query.Set("authKey", authKey)
	query.Set("appName", appname)
	query.Set("version", "1.0")
	query.Set("model", "")
	query.Set("signName", signName)
	query.Set("templateCode", tplCode)
	query.Set("phoneNumber", mobile)
	query.Set("templateParam", tplParam)
	query.Set("phoneCode", phoneCode)

	resp, err := SendPostForm(smshost+"/api/send_dh3t", query)
	if err != nil {
		result.Code = "-1"
		global.LOG.Error("send sms error, mobile: "+mobile, zap.Error(err))
		return
	}
	if err = json.Unmarshal(resp, &result); err != nil {
		result.Code = "-1"
		global.LOG.Error("mobile: "+mobile, zap.Error(err))
		return
	}
	if result.Code != "200" {
		global.LOG.Error("mobile: "+mobile, zap.String("message", result.Message))
		return
	}
	ok = true
	return
}
