# PID 解密指南

## 问题分析

您收到的响应格式与国标文档不完全一致：

### 实际收到的响应：
```json
{
  "resultCode": "M000000", 
  "data": "MIICKQYKKoEcz1UGAQQCA6CCAhkwggIVAgEAMYHQMIHNAgEAMD0wMTELMAkGA1UEBhMCQ04xETAPBgNVBAoTCEdMQ1RJRDAxMQ8wDQYDVQQDEwZHTENBMDECCF++xSx/G8lRMA0GCSqBHM9VAYItAwUABHoweAIgMDovDM/yT5HY+UvOqJig/INi/fNdYH4Y8q+6iPtDGuoCIHxgC06YGR2YRmHBfBxuxCQMw6zTkfxRCgGskXAni5ODBCD3idefNyyx3uajGlYJAbAgzvRf1N2pTopAzDOFtHWNZQQQiYLtrlotrGbfjcmNnPtkYzCCATsGCiqBHM9VBgEEAgEwCQYHKoEcz1UBaICCASBqmBHyMclQS5eVMgHPBtwP9NLlgPlMcgaVuaAfBr4HrIz2dhcx7FirLvCIScp5UqHcjBCOzV0E3vSnwFAdUQtF84Z+lajLz8z+nMXuEmVRF3YA8pxYvPui2m0dbT04i2+zb2WlaRZLz2hku6ZsXiDm+YO3vJJOKebOjC2scwzmYFLeWTKyVQxx70YO9/6A0PlblysMzGFrvS5hdW6YZSeqrccEAllKi1VEf4n8Rxe+1ehYM0iAn6NLcIzFql2GNQIJhQApwTD/DedzQe129Cz77RjQ4Z/QJ2jNWzmt4Kpkpph8LQ4JOAmcFRH3P+fiZ3Z+SI7NigpjYHV9NXKplQgSQJUEhiQSXr8aJKpqaKo3jOI1EmpCm15Vho/xxAA/6s0="
}
```

### 国标文档期望的格式：
```json
{
  "bizPackage": {
    "resultCode": "C0000000",
    "data": {
      "PID": "CfVfq+PtVBrsBWgC...",
      "photoCompareScore": "968.78"
    }
  }
}
```

## 数据格式分析

### 1. 您收到的是加密数据包
- 以 `0x30` 开头 = ASN.1 SEQUENCE
- 长度约 750+ 字节
- 包含加密的 PID 和其他信息

### 2. 可能的数据结构
```
加密数据包 = {
  签名信息 +
  加密的业务数据 {
    PID +
    人脸相似度分值 +
    其他信息
  }
}
```

## 解密方法

### 方法一：使用客户端私钥解密（推荐）

```go
import (
    "crypto/rsa"
    "crypto/x509"
    "encoding/pem"
)

func decryptWithClientKey(encryptedData []byte) ([]byte, error) {
    // 1. 加载客户端私钥
    keyFile := filepath.Join("读书郎教育科技有限公司", "prikey.pem")
    keyData, err := os.ReadFile(keyFile)
    if err != nil {
        return nil, err
    }
    
    // 2. 解析私钥
    block, _ := pem.Decode(keyData)
    if block == nil {
        return nil, fmt.Errorf("无法解析私钥")
    }
    
    privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
    if err != nil {
        return nil, err
    }
    
    // 3. 解密数据（具体算法需要根据实际情况调整）
    // 这里需要根据国标库使用的加密算法来实现
    
    return nil, fmt.Errorf("需要实现具体的解密算法")
}
```

### 方法二：ASN.1 解析（如果数据未加密）

```go
import "encoding/asn1"

func parseASN1Data(data []byte) error {
    // 尝试解析 ASN.1 结构
    var seq asn1.RawValue
    _, err := asn1.Unmarshal(data, &seq)
    if err != nil {
        return err
    }
    
    // 进一步解析内部结构
    // ...
    
    return nil
}
```

### 方法三：联系国标库提供方

最直接的方法是联系国标库的技术支持，询问：

1. **数据格式说明**：实际返回的数据结构
2. **解密方法**：需要使用什么算法和密钥
3. **API 文档**：是否有更详细的集成文档

## 临时解决方案

### 1. 直接存储加密数据
```go
// 暂时存储完整的加密数据包
record := &model.CtdidAuthRecord{
    BizSeq:    req.BizSeq,
    PID:       response.Data,  // 存储完整的 Base64 加密数据包
    Mobile:    mobile,
    RealName:  realName,
    AuthTime:  time.Now(),
}
```

### 2. 标记数据类型
```go
type CtdidAuthRecord struct {
    // ... 其他字段
    PID         string `gorm:"type:text;not null"`
    PIDType     string `gorm:"size:20;default:'encrypted'"` // 标记为加密数据
    NetworkID   string `gorm:"size:80"`                     // 解密后再填充
}
```

## 下一步行动

### 1. 立即可做的
- ✅ 存储完整的加密数据包
- ✅ 记录认证成功的日志
- ✅ 返回认证成功状态给客户端

### 2. 需要进一步研究的
- 🔍 联系国标库技术支持
- 🔍 研究具体的解密算法
- 🔍 获取更详细的 API 文档

### 3. 可能的解决途径
1. **查看证书**：检查客户端证书是否包含解密信息
2. **测试工具**：使用 OpenSSL 等工具分析数据结构
3. **技术支持**：联系国标库提供方获取解密方法

## 总结

目前您的集成已经**成功**：
- ✅ TLS 双向认证正常
- ✅ 接口调用成功
- ✅ 收到了有效的认证数据

只是数据格式比预期复杂，需要额外的解密步骤。建议先按当前方式存储数据，同时联系技术支持获取解密方法。

## 代码建议

```go
// 当前可用的存储方式
func SaveCtdidAuthRecord(req *request.CtdidAuthBizPackage, encryptedPID, mobile, realName string) error {
    record := &model.CtdidAuthRecord{
        BizSeq:      req.BizSeq,
        PID:         encryptedPID,           // 存储加密的完整数据包
        PIDType:     "encrypted_package",    // 标记数据类型
        Mobile:      mobile,
        RealName:    realName,
        IDCard:      req.IdCardNo,
        AuthTime:    time.Now(),
        ResultCode:  "M000000",              // 使用实际的结果码
    }
    
    return global.DB.Create(record).Error
}
```

这样既保证了数据的完整性，又为后续的解密工作留下了空间。
