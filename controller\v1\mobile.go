package v1

import (
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"account/global"
	"account/model"
	"account/model/response"
	"account/pkg/cryptor"
	"account/service"
	"account/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MobileRegister 手机注册
// login no
// @Param sn string true "签名"
// @Param mobile string true "手机号"
// @Param username string false "用户名" // 为空时为手机号
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Param password string true "密码"
// @Success 200 {json} json {"uid": 1, "username": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********}
// @Router /mobile/register [get]
func MobileRegister(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	username := GetRequestParam(c, "username")
	if username == "" {
		username = mobile
	} else if !utils.TestUsername(username) {
		response.FailWithErrcode(response.E_USERNAME_FMT, "", c)
		return
	}
	password := GetRequestParam(c, "password")
	if password == "" {
		response.FailWithErrcode(response.E_REQUIRE_PWD, "", c)
		return
	} else if !utils.TestPassword(password) {
		password = utils.MD5(password)
	}
	if service.MemberIsExistsByUsernameAndMobile(username, mobile) {
		response.FailWithErrcode(response.E_MEMBER_ALREADY_EXISTED, "", c)
		return
	}
	country_code, ok := CheckSMSVerify(c, mobile)
	if !ok {
		return
	}
	var m model.AcMember
	m.Username = username
	m.Mobile = mobile
	m.CountryCode = country_code
	service.FormatMemberPassword(&m, password)
	now := time.Now().Unix()
	m.AccessToken = utils.MD5(mobile + strconv.FormatInt(now, 10))
	m.AccessExpire = now + global.TOKEN_EXPIRE
	m.Regip = GetIp(c)
	m.Regdate = now
	m.Regapp = GetAppid(c)
	if err := service.CreateMember(&m); err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	} else {
		c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile, "regdate": m.Regdate})
		response.OkWithData(gin.H{
			"uid": strconv.Itoa(int(m.Uid)), "username": m.Username, "mobile": m.Mobile, "forbidden": m.Forbidden,
			"access_token": m.AccessToken, "access_expire": m.AccessExpire, "country_code": m.CountryCode,
		}, c)
	}
}

// MobileLogin 手机登录
// login no
// @Param sn string true "签名"
// @Param mobile string true "手机号"
// @Param username string false "用户名" // 在手机号未注册时使用该参数作为账号，默认为手机号
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Success 200 {json} json {"uid": 1, "username": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********, "avatar": "", "realname": "", "registered": 1}
// @Router /mobile/login [post]
func MobileLogin(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	username := GetRequestParam(c, "username")
	if username == "" {
		username = mobile
	} else if !utils.TestUsername(username) {
		response.FailWithErrcode(response.E_USERNAME_FMT, "", c)
		return
	}
	country_code, ok := CheckSMSVerify(c, mobile)
	if !ok {
		return
	}
	now := time.Now().Unix()
	timestr := strconv.FormatInt(now, 10)
	m, err := service.GetMemberByMobile(mobile)
	var register bool
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
		m.Username = username
		m.Mobile = mobile
		m.CountryCode = country_code
		m.Regip = GetIp(c)
		m.Regdate = now
		m.Regapp = GetAppid(c)
		m.Forbidden = 0
		m.AccessToken = utils.MD5(mobile + timestr)
		service.FormatMemberPassword(m, utils.MD5(""))
		register = true
	}
	if m.Forbidden == 1 {
		response.FailWithErrcode(response.E_FORBIDDEN, "", c)
		return
	}
	if !CheckDeviceLoginMember(c, m) {
		return
	}
	m.AccessExpire = now + global.TOKEN_EXPIRE
	if register {
		err = service.CreateMember(m)
	} else {
		err = service.SaveMemberAccess(m)
	}
	if err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	} else {
		c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
		data := getMemberLoginInfo(m)
		data["mobile"] = m.Mobile
		data["country_code"] = m.CountryCode
		data["access_expire"] = m.AccessExpire
		data["avatar"] = utils.GetAvatarUri(m.Uid)
		p, err := service.GetProfileByUid(m.Uid)
		if err == nil {
			data["realname"] = p.Realname
		}
		if !register {
			data["registered"] = 1
		}
		response.OkWithData(data, c)
	}
}

// MobileResetPassword 短信重置密码
// login no
// @Param sn string true "签名"
// @Param mobile string true "手机号"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Param password string true "新密码"
// @Success 200 {json} json {"access_token": "", "access_expire": **********, "status": "success"}
// @Router /mobile/resetpwd [get]
func MobileResetPassword(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	password := GetRequestParam(c, "password")
	if password == "" {
		response.FailWithErrcode(response.E_REQUIRE_PWD, "", c)
		return
	} else if !utils.TestPassword(password) {
		password = utils.MD5(password)
	}
	m, err := service.GetMemberByMobile(mobile)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "该手机未注册", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	if m.Forbidden == 1 {
		response.FailWithErrcode(response.E_FORBIDDEN, "", c)
		return
	}
	if !CheckVerify(c, mobile) {
		return
	}
	now := time.Now().Unix()
	service.FormatMemberPassword(m, password)
	m.AccessToken = utils.MD5(mobile + strconv.FormatInt(now, 10))
	m.AccessExpire = now + global.TOKEN_EXPIRE
	err = service.ResetPassword(m)
	if err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
	service.SetTryLogin(c, m, true)
	response.OkWithData(gin.H{
		"access_token": m.AccessToken, "access_expire": m.AccessExpire, "status": "success",
	}, c)
}

// MobileBind 绑定手机号
// login yes
// @Param sn string true "签名"
// @Param mobile string true "手机号"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Success 200 {json} json {"username": "", "mobile": "", "status": "success"}
// @Router /mobile/bind [get]
func MobileBind(c *gin.Context) {
	m, ok := CheckLogin(c)
	if !ok {
		return
	}
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	if _, err := service.GetMemberByMobile(mobile); err == nil {
		response.FailWithErrcode(response.E_ALREADY_BOUND, "该手机号已被绑定", c)
		return
	}
	country_code, ok := CheckSMSVerify(c, mobile)
	if !ok {
		return
	}
	if m.Username == m.Mobile {
		m.Username = mobile
	}
	m.Mobile = mobile
	m.CountryCode = country_code
	if err := service.BindMobile(m); err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	} else {
		c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
		response.OkWithData(gin.H{
			"status": "success", "username": m.Username, "mobile": m.Mobile, "country_code": m.CountryCode,
		}, c)
	}
}

// MobileRebind 重新绑定手机号
// login no，Q系列需要登录
// @Param sn string true "签名"
// @Param newmobile string true "手机号"
// @Param uid/mobile int/string true "用户id/旧手机号（二选一）"
// @Param password string true "密码，Q系列可选"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Success 200 {json} json {"username": "", "mobile": "", "status": "success"}
// @Router /mobile/rebind [get]
func MobileRebind(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	checkpwd := true
	appid := GetAppid(c)
	// 不需要检查密码的客户端，要求用户已登录
	if appid == "ac_android_qx_common_1.0" {
		if reason == 0 {
			response.FailWithDetail(response.E_INVALID_SN, "", gin.H{"reason": -7, "desp": desp}, c)
			return
		}
		checkpwd = false
	}
	newmobile := GetRequestParam(c, "newmobile")
	if encrypt, ok := GetAndCheckEncrypt(c); !ok {
		return
	} else if encrypt == 1 {
		newmobile = cryptor.AesCbcDecrypt(newmobile, c.GetString("appsec"))
	}
	if newmobile == "" {
		response.FailWithErrcode(response.E_REQUIRE_MOBILE, "", c)
		return
	} else if !utils.TestPhone(newmobile) {
		response.FailWithErrcode(response.E_MOBILE_FMT, "", c)
		return
	}
	if _, err := service.GetMemberByMobile(newmobile); err == nil {
		response.FailWithErrcode(response.E_MEMBER_ALREADY_EXISTED, "", c)
		return
	}
	var password string
	if checkpwd {
		pwd, ok := GetAndCheckPassword(c)
		password = pwd
		if !ok {
			return
		}
	}
	// 未登录，使用 uid 或 moble 参数获取账号
	if reason == 0 {
		uid := GetRequestParam(c, "uid")
		if uid == "" {
			mobile, ok := GetAndCheckMobile(c)
			if !ok {
				return
			}
			m, _ = service.GetMemberByMobile(mobile)
		} else {
			m, _ = service.GetMemberByUid(uid)
		}
	}
	if m.Uid == 0 {
		response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		return
	}
	if m.Forbidden == 1 {
		response.FailWithErrcode(response.E_FORBIDDEN, "", c)
		return
	}
	if checkpwd {
		if !service.CheckPwd(m, password) {
			response.FailWithErrcode(response.E_UNAMEPWD, "", c)
			return
		}
	}
	country_code, ok := CheckSMSVerify(c, newmobile)
	if !ok {
		return
	}
	if m.Username == m.Mobile {
		m.Username = newmobile
	}
	m.Mobile = newmobile
	m.CountryCode = country_code
	if err := service.BindMobile(m); err == nil {
		c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
		response.OkWithData(gin.H{
			"status": "success", "username": m.Username, "mobile": m.Mobile, "country_code": m.CountryCode,
		}, c)
	} else {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	}
}

// MobileVerify 获取短信验证码
// login no
// @Param sn string true "签名"
// @Param mobile string true "手机号"
// @Param country_code string false "国际区号，默认86"
// @Success 200 {json} json {"serial": "1001"}
// @Router /mobile/verify [get]
func MobileVerify(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	country_code := "86"
	if code := GetRequestParam(c, "country_code"); code != "" && code != "0" {
		country_code = code
	}
	now := time.Now().Unix()
	var v model.AcVerify
	v.Username = mobile
	v.Verify = strconv.Itoa(rand.Intn(900000) + 100000)
	v.Expire = now + global.MOBILE_VAILD
	v.Status = 1
	v.Type = 2
	v.CountryCode = country_code
	err := service.CreateVerify(&v)
	if err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	serial := strconv.Itoa(int(v.ID))
	signName := ""
	if _, isAxkjModel := axkjModel[GetDeviceModel(c)]; isAxkjModel {
		signName = "爱学空间"
	}
	if resp, ok := utils.SendSMSVerify(mobile, v.Verify, country_code, signName); !ok {
		response.FailWithDetail(response.E_SEND_SMS,
			fmt.Sprintf("错误(%s):%s", resp.Code, resp.Message),
			gin.H{"sms_status": "failed"},
			c)
	} else {
		response.OkWithData(gin.H{"serial": serial, "sms_result": resp.Code}, c)
	}
}

// MobileUnregister 手机注销账号
// login no
// @Param sn string true "签名"
// @Param mobile string true "手机号"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Success 200 {json} json {"uid": 1, "success": true}
// @Router /mobile/unregister [get]
func MobileUnregister(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	m, err := service.GetMemberByMobile(mobile)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	if !CheckVerify(c, mobile) {
		return
	}
	if err := service.MemberUnregister(m.Uid); err != nil {
		response.FailWithError(err, c)
	} else {
		c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
		global.LOG.Info("mobile/unregister", zap.Uint("uid", m.Uid), zap.String("mobile", mobile))
		service.NotifyParentManager(int(m.Uid), mobile)
		response.OkWithData(gin.H{"uid": m.Uid, "success": true}, c)
	}
}

// MobileVerify2 获取短信验证码
// login no
// @Param sn string true "签名，checkstr = mobile"
// @Param request.MobileVerify2
// @Success 200 {json} json {"serial": "1001"}
// @Router /mobile/verify2 [get | post]
func MobileVerify2(c *gin.Context) {
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	// 签名校验
	if reason, desp, _ := checkSignature2(c, 300, mobile, nil); reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, "", gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	appid := c.GetString("appid")
	// 限制短信 1min 1条
	if !service.CheckMobileVerifyLimit(mobile, appid, 2) {
		response.FailWithErrcode(response.E_FREQUENTLY, "每分钟只能发送1条验证码信息，请稍后重试", c)
		return
	}
	country_code := "86"
	if code := GetRequestParam(c, "country_code"); code != "" && code != "0" {
		country_code = code
	}
	now := time.Now().Unix()
	var v model.AcVerify
	v.Username = mobile
	v.Verify = strconv.Itoa(rand.Intn(900000) + 100000)
	v.Expire = now + global.MOBILE_VAILD
	v.Status = 1
	v.Type = 2
	v.CountryCode = country_code
	err := service.CreateVerify(&v)
	if err != nil {
		c.Error(err)
		response.FailWithErrcode(response.E_DATABASE, "", c)
		return
	}
	serial := strconv.Itoa(int(v.ID))
	signName := ""
	if _, isAxkjModel := axkjModel[GetDeviceModel(c)]; isAxkjModel {
		signName = "爱学空间"
	}
	if resp, ok := utils.SendSMSVerify(mobile, v.Verify, country_code, signName); !ok {
		response.FailWithDetail(response.E_SEND_SMS,
			fmt.Sprintf("错误(%s):%s", resp.Code, resp.Message),
			gin.H{"sms_status": "failed"},
			c)
	} else {
		response.OkWithData(gin.H{"serial": serial, "sms_result": resp.Code}, c)
	}
}

// MobileLogin2 手机登录
// login no
// @Param sn string true "签名"
// @Param mobile string true "手机号"
// @Param username string false "用户名" // 在手机号未注册时使用该参数作为账号，默认为手机号
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Success 200 {json} json {"uid": 1, "username": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********, "avatar": "", "realname": "", "registered": 1}
// @Router /mobile/login [post]
func MobileLogin2(c *gin.Context) {
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	// 签名校验
	if reason, desp, _ := checkSignature2(c, 300, mobile, nil); reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, "", gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	username := GetRequestParam(c, "username")
	if username == "" {
		username = mobile
	} else if !utils.TestUsername(username) {
		response.FailWithErrcode(response.E_USERNAME_FMT, "", c)
		return
	}
	country_code, ok := CheckSMSVerify(c, mobile)
	if !ok {
		return
	}
	now := time.Now().Unix()
	timestr := strconv.FormatInt(now, 10)
	m, err := service.GetMemberByMobile(mobile)
	var register bool
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
		m.Username = username
		m.Mobile = mobile
		m.CountryCode = country_code
		m.Regip = GetIp(c)
		m.Regdate = now
		m.Regapp = GetAppid(c)
		m.Forbidden = 0
		m.AccessToken = utils.MD5(mobile + timestr)
		service.FormatMemberPassword(m, utils.MD5(""))
		register = true
	}
	if m.Forbidden == 1 {
		response.FailWithErrcode(response.E_FORBIDDEN, "", c)
		return
	}
	if !CheckDeviceLoginMember(c, m) {
		return
	}
	m.AccessExpire = now + global.TOKEN_EXPIRE
	if register {
		err = service.CreateMember(m)
	} else {
		err = service.SaveMemberAccess(m)
	}
	if err != nil {
		response.FailWithErrcode(response.E_DATABASE, "", c)
	} else {
		c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
		data := getMemberLoginInfo(m)
		data["mobile"] = m.Mobile
		data["country_code"] = m.CountryCode
		data["access_expire"] = m.AccessExpire
		data["avatar"] = utils.GetAvatarUri(m.Uid)
		p, err := service.GetProfileByUid(m.Uid)
		if err == nil {
			data["realname"] = p.Realname
		}
		if !register {
			data["registered"] = 1
		}
		response.OkWithData(data, c)
	}
}
