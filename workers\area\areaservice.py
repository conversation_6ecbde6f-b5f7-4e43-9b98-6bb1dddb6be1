import json
import mysql.connector
import requests
from logger import log
import time
import datetime
import re

def trim_province(province):
    if len(province) < 2:
        return province
    if province.find('内蒙古') >= 0:
        return '内蒙古'
    if province.find('黑龙江') >= 0:
        return '黑龙江'
    return province[:2]

def trim_city(city):
    if len(city) <= 0:
        return city
    lst = city[-1]
    if lst == '市':
        return city[:-1]
    if city.find('恩施土家族苗族自治州') >= 0:
        return '恩施'
    elif city.find('焦作') >=0:#可能是（焦作济源）
        return '焦作'
    elif city.find('潢川') >=0:
        return '信阳'
    return city


class AreaService(object):
    def __init__(self):
        self._reconnect()

    def _reconnect(self):
        try:
            self.db = mysql.connector.connect(host="rds49edhgd0910y6ok82760.mysql.rds.aliyuncs.com",user = "user2015_ac", passwd = "readboy", database = "ac_center", buffered=True)
            self.cursor = self.db.cursor(dictionary=True)
        except:
            self.cursor = None
            
        try:
            self.dbcommon = mysql.connector.connect(host="rds30hy45pn1373h5qlt773.mysql.rds.aliyuncs.com",user = "rbcommondata", passwd = "vc0m4H9j8ybTs2Hv", database = "rbcommondata")
            self.cursor_common = self.dbcommon.cursor(dictionary=True)
        except:
            self.cursor_common = None    

    def _find_first(self, sql, params):
        #print(sql, params)
        self.cursor.execute(sql, params)
        return self.cursor.fetchone()

    def _find_all(self, sql, params):
        #print(sql, params)
        self.cursor.execute(sql, params)
        return self.cursor.fetchall()

    def _update(self, sql, params):
        if self.cursor == None: return
        #print(sql, params)
        self.cursor.execute(sql, params)
        self.db.commit()
        
    def _get_phone_area_by_common_data(self, phone):
        """
        获取手机归属地（根据号段）
        """
        #select * from phone_location where segment=$phoneseg limit 1
        
        self.cursor_common.execute("select * from phone_location where segment=%s", (phone[:7],))
        result = self.cursor_common.fetchone()
        #print(result)
        if result == None:
            return "", ""
        return result['province'], result['city']
        
    def _get_phone_area(self, phone):
        """
        根据手机归属地解析位置
        """
        try:
            url = 'https://sp0.baidu.com/8aQDcjqpAAV3otqbppnN2DJv/api.php?resource_id=6004&format=json&query=' + phone
            res = requests.request('POST', url)
            r = json.loads(res.text)
            #print(r)
            if 'data' in r and len(r['data']) > 0:
                data = r['data'][0]
                if 'prov' in data and len(data['prov']) > 0 and 'city' in data:
                    return data['prov'], data['city']
                elif 'city' in data:
                    return data['city'], data['city']
            return '',''
        except Exception as e:
            log('get_phone_area_error:', e, type(e))
            return '',''
            
    def _get_phone_area_new(self, phone):
        try:
            url = 'https://haoma.baidu.com/phoneSearch?search='+phone+'&position=&request_page=1'
            res = requests.request('GET', url)
            result = res.text.split('\n') if res.text != None else []
            for line in result:
                city = ""
                if line.find('中国移动') > -1 \
                    or line.find('中国电信') > -1 \
                    or line.find('中国联通') > -1:
                        city = line[line.find('>')+1:]
                        city = city[:city.find(' ')]
                        if city:
                            result = self._find_first('select province, city from ac_areas where city=%s LIMIT 1', (city, ))
                            if result:
                                 return result['province'], result['city']
            return '',''
        except Exception as e:
            return '',''
            
            
    def _get_ip_area(self, ip):
        """
        根据IP解析位置
        """
        try:
            url = 'https://sp0.baidu.com/8aQDcjqpAAV3otqbppnN2DJv/api.php?resource_id=6006&format=json&query=' + ip
            res = requests.request('POST', url)
            r = json.loads(res.text)
            #print(r)
            if 'data' in r and len(r['data']) > 0:
                data = r['data'][0]
                if 'location' in data and len(data['location']) > 0:
                    location = data['location']
                    
                    match = re.match(r'(.+?)(?:省|自治区|市)(.+?)市', location)
                    if match:
                        return match.groups()[0], match.groups()[1]
                    else:
                        match = re.match(r'(.+?)(?:省|自治区|市)', location)
                        if match:
                            return match.groups()[0], '其他'

            return '',''
        except Exception as e:
            log('get_phone_area_error:', e, type(e))
            return '',''
            
    def _get_by_location(self, location):#location = {'lng':'XXX.XXXXXX', 'lat':'XX.XXXXXX'}
        """
        根据经纬度解析位置，客户端需要高德地图IP白名单
        """
        try:
            url = 'http://restapi.amap.com/v3/geocode/regeo'
            res = requests.request('POST', url, data={'key':'45a9088831d2d9e147f75f602a5b3554', 'location': location})
            data = json.loads(res.text)
            #print(data)
            if 'status' in data and data['status'] == '1':
                addr = data['regeocode']['addressComponent']
                province = addr['province'] if 'province' in addr and len(addr['province']) > 0 else ''
                city = addr['city'] if 'city' in addr and len(addr['city']) > 0 else ''
                if city == '':
                    return trim_province(province), trim_province(province)
                else:
                    return trim_province(province), trim_city(city)
        except:
            pass
        return '', ''
        
    def _get_area_and_subarea(self, province, city):
        """
        根据省份、城市获取代理区域
        """
        try:
            result = self._find_first('select area, subarea from ac_areas where province=%s and city=%s', (province, city))
            if result == None:
                result = self._find_first('select area, subarea from ac_areas where province=%s and city=""', (province, ))
            if result == None:
                log('unknown area: ', province, city)
                #report_error('unknown area: ' + province +': '+ city)
                return '', ''
            return result['area'], result['subarea']
        except Exception as e:
            log('get_area_and_subarea_error:', e, type(e))
            self._reconnect()
            return '',''

    def _update_pcas(self, id, province, city, area, subarea):
        try:
            self._update('UPDATE ac_devices_uniq SET province=%s, city=%s, area=%s, subarea=%s WHERE id=%s', (province, city, area, subarea, id))
            return True
        except Exception as e:
            log('add_error:', e, type(e),id, province, city, area, subarea)
            self._reconnect()
            return False
            
    def _fetch_empty_areas(self, time_start=int(time.time()) - 7200, limit=100):
        try:
            dt = datetime.datetime.fromtimestamp(time_start)
            print("#AT", dt)
            return self._find_all('SELECT * FROM ac_devices_uniq WHERE createat > %s AND (province = "" OR province = %s) ORDER BY ID DESC LIMIT %s', (dt, '其他', limit))
        except Exception as e:
            print(e)
            return []
            
    def parse_pcas(self):
        data = self._fetch_empty_areas()
        
        for dev in data:
            prov = ''
            city = ''
            ipaddr = ''
            
            if 'location' in dev:
                loc = dev['location']
                #GPS优先
                if ',' in loc:
                    prov, city = self._get_by_location(loc)
                elif '.' in loc:
                    ipaddr = dev['location']
            if prov == '':
                #手机号段公共数据库
                prov, city = self._get_phone_area_by_common_data(dev['origin'])
            if prov == '':
                #外网数据库
                prov, city = self._get_phone_area_new(dev['origin'])
                
            #IP位置
            if prov == '' and ipaddr != '':
                prov, city = self._get_ip_area(ipaddr)
                log('getip failed', ipaddr, prov, city)
            if prov == '' and '.' in dev['origin']:#assume ip
                prov, city = self._get_ip_area(dev['origin'])
                
            if prov != '':
                prov = trim_province(prov)
                city = trim_city(city)
                area, subarea = self._get_area_and_subarea(prov,city)
                print(dev['id'], prov, city, area, subarea)
                self._update_pcas(dev['id'], prov, city, area, subarea)
            else:
                log('Area parse failure: ', dev['id'], dev['createat'], dev['origin'], dev['location'])
                
if __name__ == '__main__':
    a = AreaService()
    a.parse_pcas()