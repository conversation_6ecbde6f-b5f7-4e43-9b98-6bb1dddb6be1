# 证书目录设置指南

## 目录结构

在应用程序的工作目录下创建以下结构：

```
gin-account/                          # 应用程序根目录
├── gin-account.exe                   # 应用程序（Windows）
├── gin-account                       # 应用程序（Linux）
├── config.yaml                       # 配置文件
├── 读书郎教育科技有限公司/              # 证书目录
│   ├── cert.pem                     # 客户端证书
│   ├── prikey.txt                   # 客户端私钥
│   └── downstream_root.cer          # CA根证书
└── ...其他文件
```

## 设置步骤

### Windows 系统

1. 打开命令提示符，进入应用程序目录
2. 创建证书目录：
   ```cmd
   mkdir "读书郎教育科技有限公司"
   ```
3. 复制证书文件到该目录

### Linux/macOS 系统

1. 打开终端，进入应用程序目录
2. 创建证书目录：
   ```bash
   mkdir "读书郎教育科技有限公司"
   ```
3. 复制证书文件到该目录

## 权限设置

### Windows
```cmd
# 设置目录权限（可选）
icacls "读书郎教育科技有限公司" /grant:r Users:R
```

### Linux/macOS
```bash
# 设置目录权限
chmod 755 "读书郎教育科技有限公司"
chmod 644 "读书郎教育科技有限公司"/*.pem
chmod 644 "读书郎教育科技有限公司"/*.cer
chmod 600 "读书郎教育科技有限公司"/prikey.txt  # 私钥文件更严格的权限
```

## 验证设置

### 检查文件是否存在

**Windows:**
```cmd
dir "读书郎教育科技有限公司"
```

**Linux/macOS:**
```bash
ls -la "读书郎教育科技有限公司"/
```

### 预期输出
```
cert.pem
prikey.txt
downstream_root.cer
```

## 测试连接

运行测试脚本验证配置：

```bash
go run test-tls-path.go
```

预期看到类似输出：
```
=== TLS 客户端证书测试（跨平台版本）===
证书目录: 读书郎教育科技有限公司
客户端证书: 读书郎教育科技有限公司/cert.pem
私钥文件: 读书郎教育科技有限公司/prikey.txt
CA证书: 读书郎教育科技有限公司/downstream_root.cer

✅ 客户端证书加载成功: 读书郎教育科技有限公司/cert.pem
✅ CA根证书加载成功: 读书郎教育科技有限公司/downstream_root.cer
🔗 测试连接: https://1.30.133.75:20002/envelopData
✅ TLS连接成功！
```

## 故障排除

### 1. 目录创建失败
- 检查当前目录的写权限
- 确认目录名称正确（包含中文字符）

### 2. 证书文件读取失败
- 检查文件是否存在
- 验证文件权限
- 确认文件格式正确

### 3. 路径分隔符问题
- 代码使用 `filepath.Join()` 自动处理不同操作系统的路径分隔符
- Windows: `读书郎教育科技有限公司\cert.pem`
- Linux/macOS: `读书郎教育科技有限公司/cert.pem`

## 注意事项

1. **中文目录名**: 确保系统支持中文文件名
2. **相对路径**: 证书目录相对于应用程序的工作目录
3. **文件编码**: 证书文件应为标准的 PEM/CER 格式
4. **权限管理**: 私钥文件应设置适当的访问权限
