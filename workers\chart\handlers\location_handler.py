import json
import time
from handlers.handler import Handler
import mysql.connector

class LocationHandler(Handler):
    """
        处理设备位置信息，并保存最后5次位置信息
    """
    
    def __init__(self):
        self._reconnect()
        
    def _reconnect(self):
        try:
            self.last_connect_time = time.time()
            self.db = mysql.connector.connect(host="rds49edhgd0910y6ok82760.mysql.rds.aliyuncs.com",user = "user2015_ac", passwd = "readboy", database = "ac_center", buffered=True)
            self.cursor = self.db.cursor(dictionary=True)
        except:
            self.cursor = None
            
    def _update_locs(self, id, lng, lat):
        
        self.cursor.execute('select lastlocs from ac_devices_location where id = %s limit 1', (id, ))
        data = self.cursor.fetchone()
        if data!= None:
            locs = data['lastlocs']
            if locs == '': locs = []
            else: locs = locs.split(';')
            
            locs.append(",".join((str(int(time.time())), lng, lat)))
            if len(locs) >= 5:
                locs = locs[-5:]
                
            locs = ";".join(locs)
            self.cursor.execute("update ac_devices_location set lastlocs = %s where id = %s", (locs, id))
            self.db.commit()
        else:
            locs = ",".join((str(int(time.time())), lng, lat))
            self.cursor.execute("insert into ac_devices_location(id, lastlocs) values(%s, %s)", (id, locs))
            self.db.commit()
            
    def handle(self, message_id, message_tag, data):
        try:
            if time.time() - self.last_connect_time > 3600:
                self._reconnect()
                
            device = data['device'] if 'device' in data else None
            user = data['user'] if 'user' in data else None
            
            if device != None and 'id' in device and 'location' in device:
                if ',' in device['location']:
                    '''update device location'''
                    lng, lat = device['location'].split(',')
                    #log('LocationUpdateHandler:', 'update location: ', device['id'], lng, lat)
                    self._update_locs(device['id'], lng, lat)
                    
        except Exception as e:
            log('LocationUpdateHandler.handle error:', e, type(e))
            
if __name__ == '__main__':
    a = LocationUpdateHandler()
    a._update_locs('123', '134.0100','29.1029')