package service

import (
	"account/global"
	"account/model"
	"context"

	"gorm.io/gorm/clause"
)

func CreateUserProfileExtra(c context.Context, uc *model.UserProfileExtra) (err error) {
	err = global.GetDB(c).Model(uc).Clauses(clause.OnConflict{UpdateAll: true}).Create(uc).Error
	return
}

func UpdateUserProfileExtra(c context.Context, uc *model.UserProfileExtra) (err error) {
	err = global.GetDB(c).Model(uc).Where("uid = ?", uc.Uid).Updates(uc).Error
	return
}

func GetUserProfileExtraByCardNo(c context.Context, no string) (uc *model.UserProfileExtra, err error) {
	var u model.UserProfileExtra
	err = global.GetDB(c).Model(&u).Where(model.UserProfileExtra{CardNo: no}).Take(&u).Error
	if err == nil {
		uc = &u
	}
	return
}

func GetProfileExtraMapByUids(c context.Context, uids []uint) (m map[uint]string, err error) {
	var list []model.UserProfileExtra
	err = global.GetDB(c).Select("uid, raw_data").Model(&list).Where("uid in (?)", uids).Find(&list).Error
	if err != nil {
		return
	}
	m = make(map[uint]string)
	for _, v := range list {
		m[v.Uid] = v.RawData
	}
	return
}

func GetUserProfileExtraByUid(c context.Context, uid uint) (rawdata string, err error) {
	var u model.UserProfileExtra
	err = global.GetDB(c).Model(&u).Where("uid = ?", uid).Take(&u).Error
	if err == nil {
		rawdata = u.RawData
	}
	return
}
