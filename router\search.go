package router

import (
	v1 "account/controller/v1"

	"github.com/gin-gonic/gin"
)

func InitSearchRouter(Router *gin.RouterGroup) {
	ApiRouter := Router.Group("search")
	{
		Any(ApiRouter, "urname", v1.SearchByUrname)
		Any(ApiRouter, "username", v1.SearchByUrname)
		Any(ApiRouter, "realname", v1.SearchByRealname)
		Any(ApiRouter, "email", v1.SearchByEmail)
		Any(ApiRouter, "mobile", v1.SearchByMobile)
	}
}
