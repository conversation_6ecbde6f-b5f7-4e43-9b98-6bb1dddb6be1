package response

const (
	E_ACCESS_FORBIDDEN = 6000 // 访问禁止
	E_SEVER_CLOSED     = 6001 // 服务关闭
	E_INVALID_CLIENT   = 6002 // 无效客户端
	E_UNAUTH_MODEL     = 6003 // 未授权机型
	E_IN_BLACKLIST     = 6004 // 已列入黑名单

	E_UNKNOWN    = 7000
	E_DATABASE   = 7002
	E_PARAM      = 7004
	E_FREQUENTLY = 7005
	E_ACCESS     = 7008
	E_NOT_LOGIN  = 7009
	E_MONEY      = 7014

	E_USERNAME_FMT           = 7101
	E_MEMBER_ALREADY_EXISTED = 7104
	E_MEMBER_NOT_EXISTS      = 7105
	E_EMAIL_FMT              = 7111
	E_O_PASSWORD_FMT         = 7120
	E_PASSWORD_FMT           = 7121
	E_VERIFY_USED            = 7122
	E_VERIFY                 = 7123
	E_SERIAL_EXPIRED         = 7124
	E_ALREADY_ACTIVATED      = 7126
	E_PASSWORD               = 7129
	E_ALREADY_BOUND          = 7130
	E_FORBIDDEN              = 7131
	E_UNACTIVE               = 7132

	E_INVALID_SN       = 7200
	E_MOBILE_FMT       = 7206
	E_REQUIRE_USERNAME = 7207
	E_REQUIRE_PWD      = 7208
	E_INVALID_SERIAL   = 7212
	E_INVALID_VERIFY   = 7213
	E_ACCOUNT_MISMATCH = 7215
	E_REQUIRE_MOBILE   = 7219
	E_REQUIRE_EMAIL    = 7220
	E_INVALID_TOKEN    = 7222
	E_SEND_SMS         = 7223
	E_UNAMEPWD         = 7224

	E_PAD_DISCONN   = 7225
	E_EBAG_USERINFO = 7226
	E_LOGIN_LOCK    = 7227
)

func Errmsg(code *int) string {
	switch *code {
	case E_PARAM:
		return "参数错误"
	case E_ACCESS:
		return "错误请求"
	case E_MONEY:
		return "资金不足"
	case E_DATABASE:
		return "数据库错误"
	case E_INVALID_SN:
		return "签名无效"
	case E_UNACTIVE:
		return "账号未激活"
	case E_PASSWORD_FMT, E_O_PASSWORD_FMT:
		return "密码不合法"
	case E_PASSWORD:
		return "密码错误"
	case E_EMAIL_FMT:
		return "邮箱格式错误或无效"
	case E_MOBILE_FMT:
		return "手机格式错误"
	case E_REQUIRE_USERNAME:
		return "未填写用户名"
	case E_REQUIRE_PWD:
		return "未填写密码"
	case E_ALREADY_BOUND:
		return "已被绑定"
	case E_FORBIDDEN:
		return "账号被禁用"
	case E_MEMBER_NOT_EXISTS:
		return "账号不存在"
	case E_MEMBER_ALREADY_EXISTED:
		return "账号已存在"
	case E_INVALID_SERIAL:
		return "无效序列号"
	case E_INVALID_VERIFY:
		return "无效验证码"
	case E_VERIFY:
		return "验证码错误"
	case E_ACCOUNT_MISMATCH:
		return "账号不匹配"
	case E_SERIAL_EXPIRED:
		return "验证码已过期"
	case E_VERIFY_USED:
		return "验证码已使用"
	case E_ALREADY_ACTIVATED:
		return "账号已激活"
	case E_REQUIRE_MOBILE:
		return "手机号不能为空"
	case E_REQUIRE_EMAIL:
		return "邮箱不能为空"
	case E_USERNAME_FMT:
		return "帐号需要由6-32位英文数字或字母组成"
	case E_SEND_SMS:
		return "发送短信失败"
	case E_FREQUENTLY:
		return "频繁调用"
	case E_INVALID_TOKEN:
		return "无效token"
	case E_NOT_LOGIN:
		return "未登录"
	case E_UNAMEPWD:
		return "账号或密码错误"
	case E_PAD_DISCONN:
		return "平板链接已断开"
	case E_EBAG_USERINFO:
		return "数据请求失败"
	case E_LOGIN_LOCK:
		return "操作太频繁，此账号被锁定10分钟"
	default:
		*code = E_UNKNOWN
		return "未知错误"
	}
}
