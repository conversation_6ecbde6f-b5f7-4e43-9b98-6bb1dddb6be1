package request

type NativeDeviceBindMemberReq struct {
	Uid    int    `json:"uid" form:"uid" binding:"gt=0"`       // uid
	Serial string `json:"serial" form:"serial" binding:"gt=0"` // 序列号
}

type NativeDevicesFaceReq struct {
	Serial []string `json:"serial" binding:"gt=0"`
}
type NativeUserFaceReq struct {
	Nickname string `form:"nickname" doc:"nickname"`
	Mobile   string `form:"mobile" doc:"mobile"`
	Uid      int    `form:"uid" doc:"uid"`
	Serial   string `form:"serial" doc:"serial"`
	Model    string `form:"model" doc:"model"`
	Page     int    `form:"page" doc:"page"`
	PageSize int    `form:"page_size" doc:"page_size"`
}

type NativeUnbindDeviceMemberReq struct {
	Uid    int    `json:"uid" form:"uid" binding:"gt=0"`       // uid
	Serial string `json:"serial" form:"serial" binding:"gt=0"` // 序列号
}
