package v1

import (
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// TokenCheck 验证token
// login yes | no
// @Param sn string true "签名"
// @Param access_token string false "用户token" // 优先验证，该参数为空时要求签名为已登录
// @Param profile int false "是否返回用户昵称，0 否；1 是"
// @Success {json} json {"uid": 1, "username": "", "email": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********}
// @Router /token/check [get]
func TokenCheck(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"status": reason}, c)
		return
	}
	token := GetRequestParam(c, "access_token")
	if token != "" {
		mem, err := service.GetMemberByAccessToken(token)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				response.FailWithErrcode(response.E_INVALID_TOKEN, "", c)
			} else {
				response.FailWithErrcode(response.E_DATABASE, "", c)
			}
			return
		}
		m = mem
		c.Set("uinfo", gin.H{"uid": m.Uid, "mobile": m.Mobile})
	} else if reason == 0 {
		response.FailWithErrcode(response.E_INVALID_SN, "", c)
		return
	}
	data := getMemberLoginInfo(m)
	data["email"] = m.Email
	data["mobile"] = m.Mobile
	prof := GetRequestParam(c, "profile")
	if prof == "1" {
		p, _ := service.GetProfileByUid(m.Uid)
		data["nickname"] = p.Realname
	}
	response.OkWithData(data, c)
}

// TokenVerify 验证token
// @Param access_token string true "用户token"
// @Success {json} json {"uid": 1, "username": "", "email": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********}
// @Router /token/verify [get]
func TokenVerify(c *gin.Context) {
	token := GetRequestParam(c, "access_token")
	if token == "" {
		response.OkWithData(gin.H{"error": "no token: " + token}, c)
		return
	}
	m, err := service.GetMemberByAccessToken(token)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.OkWithData(gin.H{"error": "no token: " + token}, c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	p, err := service.GetProfileByUid(m.Uid)
	if err != nil {
		p.Uid = int(m.Uid)
		p.Username = m.Username
		p.Realname = m.Username
		service.CreateProfile(p)
	} else if p.Username != m.Username {
		p.Username = m.Username
		service.SaveProfile(p)
	}
	data := getMemberLoginInfo(m)
	data["email"] = m.Email
	data["mobile"] = m.Mobile
	data["avatar"] = utils.GetAvatarUri(m.Uid)
	data["realname"] = p.Realname
	response.OkWithData(data, c)
}
