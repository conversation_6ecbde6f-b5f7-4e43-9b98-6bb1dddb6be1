package middleware

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/service"
	"account/utils"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ControllerBase 控制器中间件
func ControllerBase() gin.HandlerFunc {
	return func(c *gin.Context) {
		firstOfAll(c)
		c.Next()
		go func(cp *gin.Context) {
			defer func() { recover() }()
			afterAll(cp)
			publish(cp)

		}(c.Copy())
	}
}

// firstOfAll 从 agent 中获取设备信息(dinfo)
// agent 中需存在 -i, -s（model 和 serial）才记录 dinfo
func firstOfAll(c *gin.Context) {
	agent := c.Request.UserAgent()
	if agent == "" {
		return
	}
	if strings.Index(agent, "-i") != 0 {
		return
	}
	ids := strings.Split(agent, "-")
	var (
		uniqno   string
		model    string
		release  string
		version  string
		did      string
		mac      string
		random   string
		serial   string
		location string
		simst    string
	)
	itemNo := 1
	for _, sec := range ids {
		if len(sec) == 0 {
			continue
		}
		func() {
			defer func() { recover() }()
			switch sec[0] {
			case 'i':
				itemNo++
				model = sec[1:]
				uniqno += "-i" + model
			case 'r':
				release = sec[1:]
				uniqno += "-r" + release
			case 'v':
				version = sec[1:]
				uniqno += "-v" + version
			case 'd':
				did = sec[1:]
				uniqno += "-d" + did
			case 't':
				random = sec[1:]
				uniqno += "-t" + random
			case 's':
				itemNo++
				serial = sec[1:]
				uniqno += "-s" + serial
			case 'm':
				mac = sec[1:]
				uniqno += "-m" + mac
			case 'l':
				loc := sec[1:]
				if len(strings.Split(loc, ",")) == 2 {
					location = loc
					c.Set("location", loc)
				}
			case 'k':
				simst = sec[1:]
			}
		}()
	}
	if itemNo < 3 {
		return
	}
	dinfo := gin.H{
		"uniqno":  uniqno,
		"model":   model,
		"serial":  serial,
		"release": release,
		"version": version,
		"did":     did,
		"mac":     mac,
		"simst":   simst,
	}
	if location != "" {
		dinfo["location"] = location
	} else {
		dinfo["location"] = getIp(c)
	}
	c.Set("dinfo", dinfo)
	if serial != "" && isForbiddenSerial(serial) {
		response.FailWithErrcode(response.E_UNKNOWN, "", c)
		c.Abort()
		return
	}
}

func isForbiddenSerial(serial string) bool {
	key := "account:forbidden_device"
	ok, _ := global.Redis.SIsMember(key, serial).Result()
	return ok
}

// afterAll 根据 uinfo 和 dinfo 创建或更新 device 和 device_uniq 信息
func afterAll(c *gin.Context) {
	val, dok := c.Get("dinfo")
	if !dok {
		return
	}
	dinfo, _ := val.(gin.H)
	val, uok := c.Get("uinfo")
	if !uok {
		return
	}
	uinfo, _ := val.(gin.H)
	if len(dinfo) == 0 || len(uinfo) == 0 {
		return
	}
	// 创建或更新 device 信息
	uniqno := dinfo["uniqno"]
	mobile, _ := uinfo["mobile"].(string)
	if mobile != "" {
		dinfo["origin"] = mobile
	} else {
		dinfo["origin"] = getIp(c)
	}
	var device model.AcDevice
	var err error
	if d, err := service.GetRedisDevice(uniqno.(string)); err == nil {
		device = *d
	} else {
		err = global.DB.Model(&device).Where("uniqno=?", uniqno).Attrs(map[string]interface{}(dinfo)).FirstOrCreate(&device).Error
		if err == nil {
			service.SetRedisDevice(device)
		}
	}
	dinfo["id"] = device.ID
	global.LOG.Info("ac_devices", zap.Any("dinfo", dinfo), zap.Error(err))
	if device.ID > 0 {
		// 设备 origin 信息从 ip 改为 mobile
		if strings.Contains(device.Origin, ".") && mobile != "" {
			device.Origin = mobile
			global.DB.Model(&model.AcDevice{}).Where("id=?", device.ID).UpdateColumn("origin", mobile)
			service.SetRedisDevice(device)
		}
	}

	// 更新 device_uniq 信息
	serial := dinfo["serial"]
	duniq := map[string]interface{}{
		"uniqno":      serial,
		"model":       dinfo["model"],
		"origin":      mobile,
		"firstbinder": uinfo["uid"],
		"province":    "", "city": "", "area": "", "subarea": "",
	}
	if dinfo["buytime"] != nil {
		duniq["bindat"] = dinfo["buytime"]
	} else {
		duniq["bindat"] = time.Now().Format(global.TIME_LAYOUT_DATECLOCK)
	}
	if location, ok := c.Get("location"); ok {
		duniq["location"] = location
	} else {
		duniq["location"] = getIp(c)
	}
	var deviceuni model.AcDevicesUniq
	err = global.DB.Model(&model.AcDevicesUniq{}).Where("uniqno=?", serial).Attrs(duniq).FirstOrCreate(&deviceuni).Error
	global.LOG.Info("ac_devices_uniq", zap.Any("duniq", duniq), zap.Error(err))
	if deviceuni.ID > 0 {
		changed := map[string]interface{}{}
		if deviceuni.Firstbinder == 0 && uinfo["uid"] != nil {
			changed["firstbinder"] = uinfo["uid"]
			changed["bindat"] = time.Now().Format(global.TIME_LAYOUT_DATECLOCK)
		}
		if deviceuni.Origin == "" && mobile != "" {
			changed["origin"] = mobile
		}
		// 设备定位信息从 ip 改为 gps
		if loc, ok := c.Get("location"); ok && !strings.Contains(deviceuni.Location, ",") {
			if location, _ := loc.(string); strings.Contains(location, ",") {
				changed["location"] = location
			}
		}
		if len(changed) > 0 {
			global.DB.Model(&model.AcDevicesUniq{}).Where("id=?", deviceuni.ID).Updates(changed)
		}
	}
	c.Set("dinfo", dinfo)
}

// publish 向阿里云消息队列发布一条消息
func publish(c *gin.Context) {
	if !global.CONFIG.App.EnableMQ {
		return
	}
	user, uok := c.Get("uinfo")
	device, dok := c.Get("dinfo")
	if !uok || user == nil || !dok || device == nil {
		return
	}
	appid, _ := c.Get("appid")
	ip := getIp(c)
	error_code, _ := c.Get("error_code")
	tag := c.Request.URL.Path
	data := gin.H{
		"user":       user,
		"device":     device,
		"appid":      appid,
		"ip":         ip,
		"time":       time.Now().Unix(),
		"error_code": error_code,
		"tag":        tag,
	}
	id, err := utils.MQPublish(utils.NewMQProducer(), data, tag)
	global.LOG.Info("publish mq msg", zap.Any("mqMsg", data), zap.String("msgId", id), zap.Error(err))
}

func getIp(c *gin.Context) string {
	if val, ok := c.Get("ip"); ok {
		return val.(string)
	} else {
		ip := c.ClientIP()
		c.Set("ip", ip)
		return ip
	}
}

func DeviceCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		firstOfAll(c)
		c.Next()
		afterAll(c)
		publish(c)
	}
}
