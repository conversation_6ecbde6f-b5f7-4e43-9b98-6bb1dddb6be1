package v1

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// WxRegister 绑定微信或用微信注册
// login no
// @Param sn string true "签名"
// @Param mobile string true "手机号"
// @Param unionid string true "微信unionid"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Param password string flase "密码，注册时必填"
// @Param sex int false "微信性别，1 男；2 女"
// @Param nickname string false "微信昵称"
// @Param province string false "微信地区（省）"
// @Param city string false "微信地区（市）"
// @Success 200 {json} json {"uid": 1, "username": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********}
// @Router /wx/register [get]
func WxRegister(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithErrcode(response.E_INVALID_SN, desp, c)
		return
	}
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	unionid, ok := GetAndCheckUnionId(c)
	if !ok {
		return
	}
	country_code, ok := CheckSMSVerify(c, mobile)
	if !ok {
		return
	}
	password := GetRequestParam(c, "password")
	if password != "" && !utils.TestPassword(password) {
		password = utils.MD5(password)
	}
	now := time.Now().Unix()
	isRegister := false // 是否为注册
	m, err := service.GetWxMember(unionid, mobile)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		if password == "" {
			response.FailWithErrcode(response.E_REQUIRE_PWD, "", c)
			return
		}
		m.Regip = GetIp(c)
		m.Regdate = now
		m.Regapp = GetAppid(c)
		service.FormatMemberPassword(m, password)
		m.AccessToken = utils.MD5(mobile + strconv.FormatInt(now, 10))
		isRegister = true
	}
	m.Username = mobile
	m.Unionid = unionid
	m.AccessExpire = now + global.TOKEN_EXPIRE
	m.Forbidden = 0
	m.Mobile = mobile
	m.CountryCode = country_code

	if isRegister {
		if err = service.CreateMember(m); err != nil {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
	} else {
		if err = service.SaveMember(m); err != nil {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
	}
	if p, err := service.GetProfileByUid(m.Uid); err == nil {
		if sex := GetRequestParam(c, "sex"); sex == "2" {
			p.Gender = 1
		} else if sex == "1" {
			p.Gender = 0
		}
		province := GetRequestParam(c, "province")
		city := GetRequestParam(c, "city")
		if province != "" {
			p.Address = province + "-" + city
		}
		p.Weixin = unionid
		if realname := GetRequestParam(c, "nickname"); realname != "" && p.Realname == "" {
			p.Realname = realname
		}
		service.SaveProfile(p)
	} else {
		p := model.UserProfile{}
		p.Uid = int(m.Uid)
		p.Username = m.Username
		if sex := GetRequestParam(c, "sex"); sex == "2" {
			p.Gender = 1
		} else if sex == "1" {
			p.Gender = 0
		}
		province := GetRequestParam(c, "province")
		city := GetRequestParam(c, "city")
		if province != "" {
			p.Address = province + "-" + city
		}
		p.Weixin = unionid
		if realname := GetRequestParam(c, "nickname"); realname != "" {
			p.Realname = realname
		}
		service.CreateProfile(&p)
	}
	data := getMemberLoginInfo(m)
	data["mobile"] = m.Mobile
	data["country_code"] = m.CountryCode
	response.OkWithData(data, c)
}

// WxLogin 微信登录
// login no
// @Param sn string true "签名"
// @Param unionid string true "微信unionid"
// @Success 200 {json} json {"uid": 1, "username": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********, "avatar": ""}
// @Router /wx/login [get]
func WxLogin(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 && reason != -2 {
		response.FailWithErrcode(response.E_INVALID_SN, desp, c)
		return
	}
	now := time.Now().Unix()
	if reason == 1 {
		m.AccessExpire = now + global.TOKEN_EXPIRE
		service.SaveMemberAccess(m)
		data := getMemberLoginInfo(m)
		data["access_expire"] = m.AccessExpire
		data["email"] = m.Email
		data["mobile"] = m.Mobile
		data["country_code"] = m.CountryCode
		data["avatar"] = utils.GetAvatarUri(m.Uid)
		response.OkWithData(data, c)
		return
	}
	unionid, ok := GetAndCheckUnionId(c)
	if !ok {
		return
	}
	m, err := service.GetMemberByUnionid(unionid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	if !CheckMemberForbidden(c, m) {
		return
	}
	if now >= m.AccessExpire {
		m.AccessToken = service.CreateMemberAccessToken(m.Uid, now)
	}
	m.AccessExpire = now + global.TOKEN_EXPIRE
	service.SaveMemberAccess(m)
	appid := GetAppid(c)
	service.CreateOrUpdateUserStatus(m.Uid, GetIp(c), appid, now)
	data := getMemberLoginInfo(m)
	data["access_expire"] = m.AccessExpire
	data["email"] = m.Email
	data["mobile"] = m.Mobile
	data["country_code"] = m.CountryCode
	data["avatar"] = utils.GetAvatarUri(m.Uid)
	response.OkWithData(data, c)
}

// WxUnregister 微信解绑
// login no
// @Param sn string true "签名"
// @Param mobile string true "手机号"
// @Param unionid string true "微信unionid"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Success {json} json {"status": 0}
// @Router /wx/unregister [get]
func WxUnregister(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	unionid, ok := GetAndCheckUnionId(c)
	if !ok {
		return
	}
	if !CheckVerify(c, mobile) {
		return
	}
	m, err := service.GetMemberByMobile(mobile)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	if m.Unionid != unionid {
		response.FailWithErrcode(response.E_ACCESS, "用户未绑定该微信号", c)
		return
	}
	m.Unionid = ""
	if err := service.SaveMember(m); err == nil {
		if p, err := service.GetProfileByUid(m.Uid); err == nil {
			p.Username = m.Username
			p.Weixin = ""
			p.Uid = int(m.Uid)
			service.SaveProfile(p)
		}
		response.OkWithData(gin.H{"status": 0}, c)
		return
	}
	response.FailWithErrcode(response.E_DATABASE, "", c)
}
