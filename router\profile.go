package router

import (
	v1 "account/controller/v1"

	"github.com/gin-gonic/gin"
)

func InitProfileRouter(Router *gin.RouterGroup) {
	ApiRouter := Router.Group("profile")
	{
		Any(ApiRouter, "get", v1.ProfileGet)
		Any(ApiRouter, "getOthers", v1.ProfileOthers)
		Any(ApiRouter, "getOthers2", v1.ProfileOthers2)
		Any(ApiRouter, "edit", v1.ProfileEdit)
		Any(ApiRouter, "getOthersSimple", v1.ProfileOthersSimple)
	}
}
