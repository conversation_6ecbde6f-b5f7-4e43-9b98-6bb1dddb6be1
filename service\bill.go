package service

import (
	"account/global"
	"account/model"
)

func GetBillById(id string) (*model.AcBill, error) {
	var b model.AcBill
	err := global.DB.Model(&model.AcBill{}).First(&b, "id=?", id).Error
	return &b, err
}

func CreateBill(b *model.AcBill) error {
	return global.DB.Model(&model.AcBill{}).Create(b).Error
}

func CreateBillByMap(m map[string]interface{}) error {
	return global.DB.Model(&model.AcBill{}).Create(&m).Error
}

func SaveBill(b *model.AcBill) error {
	return global.DB.Model(&model.AcBill{}).Where("id=?", b.ID).Limit(1).Save(b).Error
}
