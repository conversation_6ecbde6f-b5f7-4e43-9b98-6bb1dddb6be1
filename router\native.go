package router

import (
	v1 "account/controller/v1"

	"github.com/gin-gonic/gin"
)

func InitNativeRouter(Router *gin.RouterGroup) {
	ApiRouter := Router.Group("native")
	{
		Any(ApiRouter, "register", v1.NativeRegister)
		Any(ApiRouter, "searchMobile", v1.NativeSearchMobile)
		Any(ApiRouter, "searchUid", v1.NativeSearchUid)
		ApiRouter.POST("deviceBind", v1.NativeBindDeviceMember)
		ApiRouter.POST("devicesFace", v1.NativeDevicesFace)
		ApiRouter.GET("userFace", v1.NativeUserFace)
		ApiRouter.POST("deleteUserFace", v1.NativeDeleteUserFace)
		ApiRouter.POST("deviceUnbind", v1.NativeUnbindDeviceMember)
	}
}
