package model

type AcDevice struct {
	ID       uint   `json:"id" gorm:"primarykey"`
	Uniqno   string `json:"uniqno"`
	Model    string `json:"model"`
	Did      string `json:"did"`
	Mac      string `json:"mac"`
	Release  string `json:"realease"`
	Serial   string `json:"serial"`
	Version  string `json:"version"`
	Random   string `json:"random"`
	Simst    string `json:"simst"`
	Origin   string `json:"origin"`
	Province string `json:"province"`
	City     string `json:"city"`
	Area     string `json:"area"`
	Subarea  string `json:"subarea"`
}
