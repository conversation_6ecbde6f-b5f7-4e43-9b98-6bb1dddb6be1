package v1

import (
	"account/model/response"
	"account/service"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// BeanCharge 学豆充值
// login no
// @Param sn string true "签名"
// @Param uid int true "充值账号id"
// @Param amount int true "充值金额"
// @Success {json} json {model.UserStatus, "charge": amount}
// @Router /bean/charge [get]
func BeanCharge(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	appid := GetAppid(c)
	if appid != "zh_endpoint" || reason != 0 {
		response.FailWithErrcode(response.E_ACCESS, "未授权", c)
		return
	}
	uidstr := GetRequestParam(c, "uid")
	m, _ := service.GetMemberByUid(uidstr)
	if m.Uid == 0 {
		response.FailWithErrcode(response.E_PARAM, "未找到指定用户", c)
		return
	}
	amountstr := GetRequestParam(c, "amount")
	amount, _ := strconv.Atoi(amountstr)
	if amount <= 0 {
		response.FailWithErrcode(response.E_PARAM, "充值金额错误", c)
		return
	}
	uid := int(m.Uid)
	if !service.BeanCharge(uid, amount) {
		response.FailWithErrcode(response.E_UNKNOWN, "充值失败", c)
		return
	}
	service.CreateBeanlog(uid, amount, 1, "充值成功", appid)
	s, _ := service.GetUserStatusByUid(uint(uid))
	data := s.ToMap()
	data["charge"] = strconv.Itoa(amount)
	response.OkWithData(data, c)
}

// BeanPay 学豆消费
// login yes
// @Param sn string true "签名"
// @Param amount int true "消费金额"
// @Param desp string true "消费说明"
// @Success {json} json {model.UserStatus, "cost": amount, "costid": 1}
// @Router /bean/pay [get]
func BeanPay(c *gin.Context) {
	m, ok := CheckLogin(c)
	if !ok {
		return
	}
	amountstr := GetRequestParam(c, "amount")
	amount, _ := strconv.Atoi(amountstr)
	if amount <= 0 {
		response.FailWithErrcode(response.E_PARAM, "消费金额错误", c)
		return
	}
	desp := GetRequestParam(c, "desp")
	if len(desp) == 0 {
		response.FailWithErrcode(response.E_PARAM, "缺少消费说明", c)
		return
	} else if len(desp) > 255 {
		response.FailWithErrcode(response.E_PARAM, "消费说明过长", c)
		return
	}
	if !service.BeanPay(int(m.Uid), amount) {
		response.FailWithErrcode(response.E_MONEY, "余额不足", c)
		return
	}
	bl, _ := service.CreateBeanlog(int(m.Uid), amount, 2, desp, GetAppid(c))
	s, _ := service.GetUserStatusByUid(m.Uid)
	data := s.ToMap()
	data["cost"] = amount
	if bl.MlogId > 0 {
		data["costid"] = strconv.Itoa(bl.MlogId)
	}
	response.OkWithData(data, c)
}

// BeanRecord 获取学豆消费记录
// login yes
// @Param sn string true "签名"
// @Param start_time int false "开始时间"
// @Param end_time int false "结束时间"
// @Success {json} json {"desp": "学豆消费记录", "result": []model.Beanlog}
// @Router /bean/record [get]
func BeanRecord(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if reason == 0 {
		response.OkWithData(gin.H{"desp": "学豆消费记录", "result": []interface{}{}}, c)
		return
	}
	if !CheckMemberForbidden(c, m) {
		return
	}
	start := GetRequestParam(c, "start_time")
	end := GetRequestParam(c, "end_time")

	start_time, _ := strconv.ParseInt(start, 10, 64)
	end_time, _ := strconv.ParseInt(end, 10, 64)
	if end_time == 0 {
		end_time = time.Now().Unix()
	}
	list, _ := service.GetBeanLogRecord(int(m.Uid), start_time, end_time)
	data := make([]gin.H, 0, len(list))
	for _, val := range list {
		data = append(data, val.ToMap())
	}
	response.OkWithData(gin.H{"desp": "学豆消费记录", "result": data}, c)
}
