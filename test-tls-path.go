package main

import (
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"io/ioutil"
	"net/http"
	"path/filepath"
	"strings"
	"time"
)

// 测试 TLS 客户端证书配置的独立脚本（跨平台版本）
func main() {
	// 跨平台证书路径配置
	certDir := "读书郎教育科技有限公司"
	certFile := filepath.Join(certDir, "cert.pem")
	keyFile := filepath.Join(certDir, "prikey.txt")
	caFile := filepath.Join(certDir, "downstream_root.cer")
	testURL := "https://***********:20002/envelopData"

	fmt.Println("=== TLS 客户端证书测试（跨平台版本）===")
	fmt.Printf("证书目录: %s\n", certDir)
	fmt.Printf("客户端证书: %s\n", certFile)
	fmt.Printf("私钥文件: %s\n", keyFile)
	fmt.Printf("CA证书: %s\n", caFile)
	fmt.Println()
	
	// 创建TLS配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true, // 暂时跳过SSL验证
	}

	// 加载客户端证书和私钥
	cert, err := tls.LoadX509KeyPair(certFile, keyFile)
	if err != nil {
		fmt.Printf("❌ 加载客户端证书失败: %v\n", err)
		fmt.Printf("将使用不带证书的连接\n")
	} else {
		tlsConfig.Certificates = []tls.Certificate{cert}
		fmt.Printf("✅ 客户端证书加载成功: %s\n", certFile)

		// 加载CA根证书
		caCert, err := ioutil.ReadFile(caFile)
		if err != nil {
			fmt.Printf("⚠️  读取CA证书失败: %v\n", err)
		} else {
			caCertPool := x509.NewCertPool()
			if !caCertPool.AppendCertsFromPEM(caCert) {
				fmt.Printf("⚠️  解析CA证书失败\n")
			} else {
				tlsConfig.RootCAs = caCertPool
				tlsConfig.InsecureSkipVerify = false // 有CA证书时启用验证
				fmt.Printf("✅ CA根证书加载成功: %s\n", caFile)
			}
		}
	}

	// 创建 HTTP 客户端
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: tlsConfig,
		},
		Timeout: 30 * time.Second,
	}

	// 发送测试请求
	fmt.Printf("🔗 测试连接: %s\n", testURL)
	
	resp, err := client.Post(testURL, "application/octet-stream", strings.NewReader("test"))
	if err != nil {
		fmt.Printf("❌ TLS连接失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return
	}

	fmt.Printf("✅ TLS连接成功！\n")
	fmt.Printf("📊 响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("📄 响应内容: %s\n", string(body))
	
	fmt.Println("=== 测试完成 ===")
}
